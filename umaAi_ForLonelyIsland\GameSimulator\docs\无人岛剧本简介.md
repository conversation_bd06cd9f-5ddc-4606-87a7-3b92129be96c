此wen


关于发展pt
发展pt不是技能点pt，训练pt只影响岛设施的等级
发展pt的计算公式已经写在Game.h里的getTrainingPt函数下，此文档就不详细解释具体公式

关于岛设施的建设和升级还有岛训练的解释

岛训练:
具体公式还没搞明白，理解为，在获得岛训练券之后，如果使用级替代一回合的训练，不消耗体力，
具体加成等计算公式暂不清楚，岛训练和普通训练一样也能获得发展pt，不过暂不清楚是如何计算的公式

岛设施:
有6种岛设施,分别是 速度 耐力 力量 根性 智力 海澜之家

速耐力根智 五种岛设施在相同等级占用的格子数量是一样的 
在 1 级 和 2 级 只占用1格建设方案 
3级会选择两个升级方向，本能全开是占用 2格 ， 熟练技巧是占用 1格
4级 本能全开 占用 2格 ， 熟练技巧占用 1格
5级 本能全开 占用 3格 ， 熟练技巧占用 2格

海澜之家 1级占用 2格 2级占用 3格 3级占用 3格 3级及是满级

具体数值加成我暂时还没写入，待补充

普通训练 和 合宿夏训 的具体数值 和 加成 我也还没写入，此文档暂时只是描述框架 待补充




回合数 12
出道前
11回合训练 + 1回合比赛（出道战）

1,2回合不计入发展pt

3回合开始前
塔可加入训练（友人可以出现在五个训练之中）
6+5一共11张r卡会随机出现在6个训练当中，不会闪彩
开始选择建设方案
第一次建设方案
一共四格
一阶段
2格
二阶段
2格
两个阶段完成之后都会获得岛训练券

速度 | 耐力
力量 | 根性
智力 | 海澜之家

一阶段达成需要200发展pt

二阶段达成需要200发展pt

出道战赢得获取
随机三维+4
pt+43
理事长羁绊+4
发展pt+60

第一次评价会大好评结算
超过400发展pt之后每10发展pt提供1点体力回复最多20体力回复
五维+10
pt+60
随机支援卡的启发技能（受支援卡本身启发等级影响）
理事长羁绊+5


第二次建设方案
如果是大好评发展pt会获得5%的提升
如果是好评发展pt会获得3%的提升

一共5格
一阶段
2格
二阶段
3格

一阶段达成需要200发展pt

二阶段达成需要300发展pt

7月上到12月下结束第一年下半一共12回合

7月下记者出现在训练当中


第二次评价会大好评结算
超过500发展pt之后每10发展pt提供1点体力回复最多20体力回复
五维提升15点
pt提升120
训练设施5个都提升一级
理事长羁绊+5
随机技能
随机3个r卡会加入6个训练当中，不能闪彩，不算训练，只算人头


塔克给头绪+3
体力每个等级都不一样默认3破给23体力回复
干劲提升1级
速度提升12

第三次建设方案
如果是大好评发展pt会获得10%的提升
如果是好评发展pt会获得5%的提升

一共6格
一阶段
3格
二阶段
3格

一阶段达成需要300发展pt

二阶段达成需要300发展pt

六月下
第三次评价会大好评结算
超过600发展pt之后每10发展pt提供1点体力回复最多25体力回复
五维提升20点
pt提升150
理事长羁绊+5
随机技能

七月上到八月下都是夏季合宿

第四次建设方案
如果是大好评发展pt会获得10%的提升
如果是好评发展pt会获得5%的提升

一共7格
一阶段
3格
二阶段
4格

一阶段达成需要300发展pt

二阶段达成需要400发展pt

第二年十二月后
第四次评价会大好评结算
超过600发展pt之后每10发展pt提供1点体力回复最多25体力回复
五维提升25点
pt提升200
理事长羁绊+5
随机技能
随机了几个r卡目前不知道具体数量

第五次建设方案
如果是大好评发展pt会获得15%的提升
如果是好评发展pt会获得10%的提升

一共8格
一阶段
4格
二阶段
4格

一阶段达成需要400发展pt

二阶段达成需要400发展pt

第三年六月下
第五次评价会大好评结算
超过800发展pt之后每10发展pt提供1点体力回复最多30体力回复
五维提升35点
pt提升300
理事长羁绊+5
随机技能
全身心+1

七月上到八月下四回合夏训
夏训期间全部训练满折 消耗体力-15

九月上至十二月下，8回合正常训练，直接获得2个岛训券

接下来进入ura回合
一回合夏训
一回合URA初赛
一回合夏训
一回合URA半决赛
一回合夏训
一回合URA决赛







---------------------以下为发展pt/训练pt的计算过程以及公式和上文无关联-----------------------------

需要计算训练pt的详细公式

4个情况
情况1
1个闪光卡训练 + 1个普通卡训练
训练pt = 93
情况2
1个闪光卡训练 + 2个普通卡训练
训练pt = 101
情况3
1个闪光卡训练 + 3个普通卡训练
训练pt = 109
情况4
1个闪光卡训练 + 3个普通卡训练
训练pt = 100
情况5
1个闪光卡训练 + 1个普通卡训练
训练pt = 90
情况6
1个闪光卡训练 + 4个普通卡训练
训练pt = 117
情况7
1个闪光卡训练 + 无普通卡
训练pt = 85
情况8
1个闪光卡训练 + 4个普通卡训练
训练pt = 121
情况9
2个闪光卡训练 + 3个普通卡训练
训练pt = 117


所有情况基础pt都为60
情况1，2，3，6，7，9都有存在一个10%的pt加成
情况4不存在pt加成
情况5存在5%的pt加成
情况8存在15%的pt加成

pt加成的乘算的位置我可能搞错了，请你设法找出具体的位置，和整个详细的训练pt计算公式

取整方式和cpp当中强制转换的取整方式相同，可能存在我没有列出的隐藏倍率，需要你自己分析推导

训练pt = 60 + (10 + 70×加成率) + 闪光卡数量×(5 + 30×加成率) + 普通卡数量×(5 + 30×加成率)


1.无闪彩
2.有闪彩 总卡数=5 加成率=0.15 - 1 训练pt = 74 + 30×加成率 + 总卡数×(6.5 + 15×加成率)

Y= 74 + 30X + a(6.5 + 15X)