# 🏗️ URA剧本AI项目结构说明

## 📁 项目目录结构

```
umaAi_ForURA/
├── 📂 GameSimulator/          # 🎮 游戏模拟器核心
│   ├── Game.h                 # 游戏状态定义
│   ├── Game.cpp               # 游戏逻辑实现
│   ├── EventSystem.h          # 事件系统 (开发中)
│   └── SupportCard.h          # 支援卡系统 (开发中)
│
├── 📂 DataExtractor/          # 🔧 数据提取工具 (独立)
│   ├── br_extractor.py        # 主提取工具
│   ├── extract.bat            # 启动脚本
│   └── README.md              # 使用说明
│
├── 📂 Tools/                  # 🛠️ 开发工具 (待整理)
│   ├── test_game.cpp          # 测试程序和AI
│   └── [其他开发工具...]
│
├── 📂 Database/               # 💾 提取的游戏数据
│   ├── events_extracted.json  # 事件数据
│   └── [其他数据文件...]
│
├── 📂 Config/                 # ⚙️ 配置文件
│   └── game_config.json       # 游戏参数配置
│
├── 📂 bin/                    # 🔨 编译输出
│   ├── test_game.exe          # 测试程序
│   └── [编译产物...]
│
├── 📂 events/                 # 📦 原始数据文件
│   ├── events.br              # 原始事件数据
│   ├── names.br               # 原始名称数据
│   └── [其他.br文件...]
│
├── 📂 Practice/               # 📚 C++学习资料
│   ├── [练习文件...]
│   └── [答案文档...]
│
├── 🔧 compile.cmd             # 编译脚本
├── 📖 README.md               # 项目说明
├── 📖 快速开始.md             # 快速开始指南
├── 📖 编译运行指南.md         # 编译指南
└── 📖 项目结构说明.md         # 本文件
```

## 🎯 各目录功能说明

### 🎮 GameSimulator/ - 游戏模拟器核心
**用途**: 包含游戏逻辑的核心实现
- `Game.h/cpp` - 完整的URA剧本游戏模拟
- `EventSystem.h` - 事件系统框架 (待完善)
- `SupportCard.h` - 支援卡系统框架 (待完善)

**状态**: ✅ 基础功能完成，🔄 高级功能开发中

### 🔧 DataExtractor/ - 数据提取工具
**用途**: 独立的.br文件解压工具
- `br_extractor.py` - 主要提取程序
- `extract.bat` - 一键启动脚本
- `README.md` - 详细使用说明

**状态**: ✅ 完全独立，可单独使用

**使用方法**:
```cmd
cd DataExtractor
extract.bat
# 或拖拽.br文件到extract.bat上
```

### 🛠️ Tools/ - 开发工具
**用途**: 各种开发和测试工具
- `test_game.cpp` - 主要的测试程序和AI实现
- 其他辅助工具 (待整理)

**计划**: 将逐步整理和分类

### 💾 Database/ - 提取的数据
**用途**: 存放从.br文件提取的JSON数据
- `events_extracted.json` - 14,122个游戏事件
- 其他提取的数据文件

**注意**: 这些是处理后的数据，用于AI开发

### 📦 events/ - 原始数据
**用途**: 存放原始的.br格式数据文件
- `events.br` - 原始事件数据
- `names.br` - 原始名称数据
- 其他游戏数据文件

**注意**: 这些是原始文件，需要用DataExtractor处理

## 🚀 使用流程

### 1. 数据提取流程
```
原始.br文件 → DataExtractor → JSON数据 → Database/
```

### 2. AI开发流程
```
Database/数据 → GameSimulator → 测试 → 优化
```

### 3. 编译测试流程
```
源代码 → compile.cmd → bin/ → 运行测试
```

## 📋 文件迁移计划

### ✅ 已完成
- [x] 创建独立的DataExtractor目录
- [x] 迁移核心提取功能
- [x] 创建简化的启动脚本
- [x] 编写独立的使用说明

### 🔄 进行中
- [ ] 整理Tools目录中的工具
- [ ] 清理重复的脚本文件
- [ ] 统一文档格式

### 📋 待完成
- [ ] 创建AI训练目录结构
- [ ] 整理学习资料目录
- [ ] 创建发布版本目录

## 💡 使用建议

### 新用户
1. **先使用DataExtractor** - 提取需要的游戏数据
2. **查看Database目录** - 了解数据结构
3. **运行测试程序** - 体验AI效果
4. **学习C++基础** - 使用Practice目录

### 开发者
1. **专注GameSimulator** - 核心游戏逻辑
2. **使用DataExtractor** - 处理新的数据文件
3. **扩展Tools** - 添加开发工具
4. **维护Database** - 管理数据版本

### 数据分析师
1. **使用DataExtractor** - 提取所需数据
2. **分析Database内容** - 理解游戏机制
3. **提供反馈** - 帮助改进AI策略

## 🔧 维护指南

### 定期清理
- 删除bin目录中的临时文件
- 清理Tools目录中的测试文件
- 更新Database中的数据

### 版本管理
- 重要更改前备份Database
- 记录数据文件的来源和版本
- 维护项目文档的更新

### 性能优化
- 监控编译输出大小
- 优化数据文件存储
- 清理不必要的依赖

---

**文档版本**: v1.0  
**更新时间**: 2025-01-22  
**维护者**: URA AI Project Team
