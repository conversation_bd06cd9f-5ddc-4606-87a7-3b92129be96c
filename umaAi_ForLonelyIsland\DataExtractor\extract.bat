@echo off
echo UMA Data Extractor v2.0
echo =======================

REM Change to script directory
cd /d "%~dp0"

REM Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    echo Please install Python 3.8+
    pause
    exit /b 1
)

REM Install brotli if needed
python -c "import brotli" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing brotli library...
    pip install brotli
)

REM Run extractor
if "%1"=="" (
    python br_extractor.py
) else (
    if "%2"=="" (
        python br_extractor.py "%1"
    ) else (
        python br_extractor.py "%1" "%2"
    )
)

pause
