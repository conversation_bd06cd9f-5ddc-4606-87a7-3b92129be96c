{"game_info": {"script_name": "URA", "script_display_name": "URA剧本", "total_turns": 78, "description": "最基础的赛马娘剧本，适合新手学习"}, "training_config": {"base_stamina_cost": 20, "base_fail_rate": 20, "motivation_bonus": {"1": -0.2, "2": -0.1, "3": 0.0, "4": 0.1, "5": 0.2}, "shining_bonus": 1.5, "friendship_gain_per_training": 5}, "race_schedule": [{"turn": 13, "name": "ホープフルS", "grade": "G1", "fan_reward_win": 1000, "fan_reward_lose": 200, "skill_reward_win": 20, "skill_reward_lose": 5}, {"turn": 24, "name": "皐月賞", "grade": "G1", "fan_reward_win": 1200, "fan_reward_lose": 300, "skill_reward_win": 25, "skill_reward_lose": 8}, {"turn": 37, "name": "日本ダービー", "grade": "G1", "fan_reward_win": 1500, "fan_reward_lose": 400, "skill_reward_win": 30, "skill_reward_lose": 10}, {"turn": 48, "name": "菊花賞", "grade": "G1", "fan_reward_win": 1300, "fan_reward_lose": 350, "skill_reward_win": 28, "skill_reward_lose": 9}, {"turn": 61, "name": "天皇賞(春)", "grade": "G1", "fan_reward_win": 1400, "fan_reward_lose": 380, "skill_reward_win": 32, "skill_reward_lose": 12}, {"turn": 72, "name": "ジャパンC", "grade": "G1", "fan_reward_win": 1600, "fan_reward_lose": 450, "skill_reward_win": 35, "skill_reward_lose": 15}, {"turn": 75, "name": "URA決勝1回戦", "grade": "URA", "fan_reward_win": 2000, "fan_reward_lose": 500, "skill_reward_win": 40, "skill_reward_lose": 20}, {"turn": 76, "name": "URA決勝2回戦", "grade": "URA", "fan_reward_win": 2500, "fan_reward_lose": 600, "skill_reward_win": 50, "skill_reward_lose": 25}, {"turn": 77, "name": "URA決勝3回戦", "grade": "URA", "fan_reward_win": 3000, "fan_reward_lose": 800, "skill_reward_win": 60, "skill_reward_lose": 30}], "event_config": {"random_event_probability": 15, "event_types": [{"id": 0, "name": "属性提升事件", "description": "随机提升一个属性", "effects": {"attribute_gain": [10, 20], "skill_points": 0, "vital_change": 0}}, {"id": 1, "name": "技能点获得事件", "description": "获得技能点", "effects": {"attribute_gain": [0, 0], "skill_points": [20, 30], "vital_change": 0}}, {"id": 2, "name": "体力恢复事件", "description": "恢复体力", "effects": {"attribute_gain": [0, 0], "skill_points": 0, "vital_change": 30}}]}, "rest_config": {"vital_recovery": 50, "motivation_up_probability": 30, "max_vital": 100, "max_motivation": 5}, "outgoing_config": {"vital_recovery": 20, "skill_points_gain": [5, 14], "motivation_change_probability": 20}, "initial_status": {"default_character": {"speed": 100, "stamina": 100, "power": 100, "guts": 100, "wisdom": 100, "speed_limit": 1200, "stamina_limit": 1200, "power_limit": 1200, "guts_limit": 1200, "wisdom_limit": 1200}}, "support_card_config": {"max_cards": 6, "friendship_for_shining": 80, "shining_probability": 50, "max_friendship": 100}, "scoring_config": {"attribute_weight": 1.0, "skill_points_weight": 10.0, "fan_count_weight": 0.01, "race_win_bonus": 100, "perfect_race_bonus": 500}, "ai_hints": {"low_vital_threshold": 30, "preferred_training_balance": [0.2, 0.2, 0.2, 0.2, 0.2], "rest_priority_when_low_vital": true, "focus_weak_attribute": true, "shining_card_priority": true}}