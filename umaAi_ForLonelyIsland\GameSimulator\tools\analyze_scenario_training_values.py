#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
剧本基础训练值分析工具
分析master.mdb中不同剧本的基础训练数值差异
"""

import sqlite3
import pandas as pd
import json
from pathlib import Path

class ScenarioTrainingAnalyzer:
    def __init__(self, mdb_path):
        """初始化分析器"""
        self.mdb_path = Path(mdb_path)
        self.conn = None
        self.training_names = {
            101: "速度训练",
            102: "耐力训练", 
            103: "力量训练",
            104: "根性训练",
            105: "智慧训练"
        }
        self.target_names = {
            1: "速度属性",
            2: "耐力属性",
            3: "力量属性", 
            4: "根性属性",
            5: "智慧属性",
            10: "体力消耗",
            30: "技能点"
        }
    
    def connect(self):
        """连接数据库"""
        try:
            self.conn = sqlite3.connect(self.mdb_path)
            print(f"✅ 成功连接数据库: {self.mdb_path}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
    
    def get_table_info(self, table_name):
        """获取表结构信息"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"\n📋 表 {table_name} 的结构:")
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
            return columns
        except Exception as e:
            print(f"❌ 获取表结构失败: {e}")
            return None
    
    def find_scenario_tables(self):
        """查找所有与剧本相关的表"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%scenario%'")
            scenario_tables = cursor.fetchall()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%single_mode%'")
            single_mode_tables = cursor.fetchall()
            
            print("\n🔍 找到的剧本相关表:")
            for table in scenario_tables:
                print(f"  📊 {table[0]}")
            
            print("\n🔍 找到的single_mode相关表:")
            for table in single_mode_tables:
                print(f"  📊 {table[0]}")
            
            return scenario_tables, single_mode_tables
        except Exception as e:
            print(f"❌ 查找表失败: {e}")
            return [], []
    
    def analyze_training_effect_table(self):
        """分析single_mode_training_effect表"""
        print("\n🎯 分析 single_mode_training_effect 表...")
        
        try:
            # 检查表是否存在
            cursor = self.conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='single_mode_training_effect'")
            if not cursor.fetchone():
                print("❌ single_mode_training_effect 表不存在")
                return None
            
            # 获取表结构
            self.get_table_info('single_mode_training_effect')
            
            # 查看所有剧本ID
            df_scenarios = pd.read_sql_query("""
                SELECT DISTINCT scenario_id, COUNT(*) as record_count
                FROM single_mode_training_effect 
                GROUP BY scenario_id
                ORDER BY scenario_id
            """, self.conn)
            
            print(f"\n📊 发现 {len(df_scenarios)} 个剧本:")
            for _, row in df_scenarios.iterrows():
                print(f"  剧本 {row['scenario_id']}: {row['record_count']} 条记录")
            
            return df_scenarios
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None
    
    def get_scenario_training_matrix(self, scenario_id):
        """获取指定剧本的完整训练矩阵"""
        print(f"\n🎯 获取剧本 {scenario_id} 的训练矩阵...")
        
        try:
            query = """
                SELECT 
                    command_id,
                    target_type,
                    effect_value,
                    sub_id,
                    result_state
                FROM single_mode_training_effect 
                WHERE scenario_id = ? 
                  AND command_id BETWEEN 101 AND 105
                ORDER BY command_id, target_type
            """
            
            df = pd.read_sql_query(query, self.conn, params=[scenario_id])
            
            if df.empty:
                print(f"❌ 剧本 {scenario_id} 没有训练数据")
                return None
            
            print(f"✅ 找到 {len(df)} 条训练数据")
            
            # 创建训练矩阵
            matrix = {}
            for _, row in df.iterrows():
                training_name = self.training_names.get(row['command_id'], f"训练{row['command_id']}")
                target_name = self.target_names.get(row['target_type'], f"类型{row['target_type']}")
                
                if training_name not in matrix:
                    matrix[training_name] = {}
                
                matrix[training_name][target_name] = {
                    'value': row['effect_value'],
                    'sub_id': row['sub_id'],
                    'result_state': row['result_state']
                }
            
            return matrix
            
        except Exception as e:
            print(f"❌ 获取训练矩阵失败: {e}")
            return None
    
    def compare_scenarios(self, scenario_ids):
        """对比多个剧本的训练值差异"""
        print(f"\n🔄 对比剧本 {scenario_ids} 的训练值差异...")
        
        matrices = {}
        for scenario_id in scenario_ids:
            matrix = self.get_scenario_training_matrix(scenario_id)
            if matrix:
                matrices[scenario_id] = matrix
        
        if len(matrices) < 2:
            print("❌ 需要至少2个剧本才能对比")
            return None
        
        # 分析差异
        differences = {}
        base_scenario = min(scenario_ids)
        base_matrix = matrices[base_scenario]
        
        for scenario_id, matrix in matrices.items():
            if scenario_id == base_scenario:
                continue
                
            scenario_diff = {}
            for training_name, training_data in base_matrix.items():
                if training_name not in matrix:
                    continue
                    
                training_diff = {}
                for target_name, base_data in training_data.items():
                    if target_name not in matrix[training_name]:
                        continue
                    
                    base_value = base_data['value']
                    compare_value = matrix[training_name][target_name]['value']
                    diff = compare_value - base_value
                    diff_percent = (diff / base_value * 100) if base_value != 0 else 0
                    
                    if diff != 0:  # 只记录有差异的
                        training_diff[target_name] = {
                            'base_value': base_value,
                            'compare_value': compare_value,
                            'difference': diff,
                            'difference_percent': round(diff_percent, 2)
                        }
                
                if training_diff:
                    scenario_diff[training_name] = training_diff
            
            if scenario_diff:
                differences[f"剧本{scenario_id}_vs_剧本{base_scenario}"] = scenario_diff
        
        return differences
    
    def export_results(self, results, output_path):
        """导出分析结果"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"✅ 结果已导出到: {output_path}")
        except Exception as e:
            print(f"❌ 导出失败: {e}")

def main():
    """主函数"""
    print("🎮 赛马娘剧本基础训练值分析工具")
    print("=" * 50)
    
    # 数据库路径
    mdb_path = Path(__file__).parent.parent / "data" / "Database" / "mdb" / "master.mdb"
    
    if not mdb_path.exists():
        print(f"❌ 数据库文件不存在: {mdb_path}")
        return
    
    # 创建分析器
    analyzer = ScenarioTrainingAnalyzer(mdb_path)
    
    try:
        # 连接数据库
        if not analyzer.connect():
            return
        
        # 查找相关表
        analyzer.find_scenario_tables()
        
        # 分析训练效果表
        scenarios_df = analyzer.analyze_training_effect_table()
        if scenarios_df is None:
            return
        
        # 获取所有剧本的训练矩阵
        all_matrices = {}
        scenario_ids = scenarios_df['scenario_id'].tolist()
        
        for scenario_id in scenario_ids:
            matrix = analyzer.get_scenario_training_matrix(scenario_id)
            if matrix:
                all_matrices[scenario_id] = matrix
        
        # 对比剧本差异
        if len(scenario_ids) > 1:
            differences = analyzer.compare_scenarios(scenario_ids)
            
            # 输出差异分析
            if differences:
                print("\n📊 剧本训练值差异分析:")
                for comparison, diff_data in differences.items():
                    print(f"\n🔍 {comparison}:")
                    for training, targets in diff_data.items():
                        print(f"  {training}:")
                        for target, data in targets.items():
                            print(f"    {target}: {data['base_value']} → {data['compare_value']} "
                                  f"(差值: {data['difference']:+}, {data['difference_percent']:+.1f}%)")
            else:
                print("\n✅ 所有剧本的训练基础值完全相同！")
        
        # 导出结果
        output_data = {
            'scenarios': scenarios_df.to_dict('records'),
            'training_matrices': all_matrices,
            'differences': differences if 'differences' in locals() else {}
        }
        
        output_path = Path(__file__).parent / "scenario_training_analysis.json"
        analyzer.export_results(output_data, output_path)
        
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
