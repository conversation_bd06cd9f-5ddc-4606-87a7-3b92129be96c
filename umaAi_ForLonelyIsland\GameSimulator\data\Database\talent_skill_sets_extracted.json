{"100101": [{"SkillId": 200512, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200732, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200162, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201351, "Rank": 3, "UpgradeSkills": {"100101111": [{"ConditionId": 10010101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10010102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 200612, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200511, "Rank": 5, "UpgradeSkills": {"100101211": [{"ConditionId": 10010103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10010104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10010105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100102": [{"SkillId": 200462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200592, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201132, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200212, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200591, "Rank": 3, "UpgradeSkills": {"100102111": [{"ConditionId": 10010201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10010202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201611, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200461, "Rank": 5, "UpgradeSkills": {"100102211": [{"ConditionId": 10010203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10010204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10010205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100103": [{"SkillId": 200332, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201212, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200612, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201172, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201211, "Rank": 3, "UpgradeSkills": {"100103111": [{"ConditionId": 10010301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10010302, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10010303, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201182, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202061, "Rank": 5, "UpgradeSkills": {"100103211": [{"ConditionId": 10010304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10010305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100201": [{"SkillId": 200432, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200552, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200712, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200022, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200431, "Rank": 3, "UpgradeSkills": {"100201111": [{"ConditionId": 10020101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10020102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10020103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200542, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200551, "Rank": 5, "UpgradeSkills": {"100201211": [{"ConditionId": 10020104, "Type": 2, "Group": 0, "Requirement": 202051, "AdditionalRequirement": 0}, {"ConditionId": 10020105, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 4}]}}], "100202": [{"SkillId": 200542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200382, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202692, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201252, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200541, "Rank": 3, "UpgradeSkills": {"100202111": [{"ConditionId": 10020201, "Type": 2, "Group": 0, "Requirement": 202051, "AdditionalRequirement": 0}, {"ConditionId": 10020202, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 10020203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10020204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10020205, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}], "100202121": [{"ConditionId": 10020209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201611, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202691, "Rank": 5, "UpgradeSkills": {"100202211": [{"ConditionId": 10020206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10020207, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10020208, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "100301": [{"SkillId": 200452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201132, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201332, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200502, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201331, "Rank": 3, "UpgradeSkills": {"100301111": [{"ConditionId": 10030101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10030102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10030103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201142, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201131, "Rank": 5, "UpgradeSkills": {"100301211": [{"ConditionId": 10030104, "Type": 6, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10030105, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "100302": [{"SkillId": 200452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201142, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200172, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200571, "Rank": 3, "UpgradeSkills": {"100302111": [{"ConditionId": 10030201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10030202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10030203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200352, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201141, "Rank": 5, "UpgradeSkills": {"100302211": [{"ConditionId": 10030204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10030205, "Type": 6, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "100303": [{"SkillId": 201902, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200562, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202482, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201611, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201901, "Rank": 3, "UpgradeSkills": {"100303111": [{"ConditionId": 10030301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10030302, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10030303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201312, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200561, "Rank": 5, "UpgradeSkills": {"100303211": [{"ConditionId": 10030304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10030305, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10030306, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10030307, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "100303221": [{"ConditionId": 10030308, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10030309, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10030310, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100401": [{"SkillId": 200362, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201052, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201062, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201051, "Rank": 3, "UpgradeSkills": {"100401111": [{"ConditionId": 10040101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10040102, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10040103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200432, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201061, "Rank": 5, "UpgradeSkills": {"100401211": [{"ConditionId": 10040104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100402": [{"SkillId": 200352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200682, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201282, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201571, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201281, "Rank": 3, "UpgradeSkills": {"100402111": [{"ConditionId": 10040201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10040202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10040203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201082, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200681, "Rank": 5, "UpgradeSkills": {"100402211": [{"ConditionId": 10040204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10040205, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}]}}], "100403": [{"SkillId": 201032, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200682, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203462, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201071, "Rank": 3, "UpgradeSkills": {"100403111": [{"ConditionId": 10040301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10040302, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10040303, "Type": 1, "Group": 2, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 10040304, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}]}}, {"SkillId": 201052, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203461, "Rank": 5, "UpgradeSkills": {"100403211": [{"ConditionId": 10040305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10040306, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100501": [{"SkillId": 200771, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201052, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201071, "Rank": 3, "UpgradeSkills": {"100501111": [{"ConditionId": 10050101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10050102, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10050103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201042, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200571, "Rank": 5, "UpgradeSkills": {"100501211": [{"ConditionId": 10050104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10050105, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10050106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "100501221": [{"ConditionId": 10050107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10050108, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10050109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100502": [{"SkillId": 200771, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201042, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200582, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201332, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200772, "Rank": 3, "UpgradeSkills": {"100502111": [{"ConditionId": 10050201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10050202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10050203, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}], "100502121": [{"ConditionId": 10050207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10050208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10050209, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201902, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200581, "Rank": 5, "UpgradeSkills": {"100502211": [{"ConditionId": 10050204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10050205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10050206, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}], "100601": [{"SkillId": 200342, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201062, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200222, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201351, "Rank": 3, "UpgradeSkills": {"100601111": [{"ConditionId": 10060101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10060102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "100601121": [{"ConditionId": 10060106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10060107, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 200492, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200341, "Rank": 5, "UpgradeSkills": {"100601211": [{"ConditionId": 10060103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10060104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10060105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100602": [{"SkillId": 201621, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200752, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201422, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201351, "Rank": 3, "UpgradeSkills": {"100602111": [{"ConditionId": 10060201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10060202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10060203, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201571, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200751, "Rank": 5, "UpgradeSkills": {"100602211": [{"ConditionId": 10060204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10060205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10060206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100701": [{"SkillId": 201591, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201212, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201472, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201482, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201471, "Rank": 3, "UpgradeSkills": {"100701111": [{"ConditionId": 10070101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10070102, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10070103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200622, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201481, "Rank": 5, "UpgradeSkills": {"100701211": [{"ConditionId": 10070104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10070105, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 2}, {"ConditionId": 10070106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100702": [{"SkillId": 201562, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200752, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201482, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202102, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200751, "Rank": 3, "UpgradeSkills": {"100702111": [{"ConditionId": 10070201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10070202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10070203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10070204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202032, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202101, "Rank": 5, "UpgradeSkills": {"100702211": [{"ConditionId": 10070205, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 4}, {"ConditionId": 10070206, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}, {"ConditionId": 10070207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "100702221": [{"ConditionId": 10070208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10070209, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 4}]}}], "100703": [{"SkillId": 200642, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200632, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200641, "Rank": 3, "UpgradeSkills": {"100703111": [{"ConditionId": 10070301, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10070302, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10070303, "Type": 1, "Group": 2, "Requirement": 4, "AdditionalRequirement": 2}, {"ConditionId": 10070304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201102, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200164, "Rank": 5, "UpgradeSkills": {"100703211": [{"ConditionId": 10070305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10070306, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10070307, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10070308, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100801": [{"SkillId": 200382, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200372, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200381, "Rank": 3, "UpgradeSkills": {"100801111": [{"ConditionId": 10080101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10080102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10080103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201032, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200701, "Rank": 5, "UpgradeSkills": {"100801211": [{"ConditionId": 10080104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10080105, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10080106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100802": [{"SkillId": 202452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202152, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200491, "Rank": 3, "UpgradeSkills": {"100802111": [{"ConditionId": 10080201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201382, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202451, "Rank": 5, "UpgradeSkills": {"100802211": [{"ConditionId": 10080202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10080203, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}], "100901": [{"SkillId": 200282, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200722, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200562, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200721, "Rank": 3, "UpgradeSkills": {"100901111": [{"ConditionId": 10090101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10090102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10090103, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}, {"SkillId": 200582, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200571, "Rank": 5, "UpgradeSkills": {"100901211": [{"ConditionId": 10090104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10090105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "100902": [{"SkillId": 202462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201282, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201172, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201281, "Rank": 3, "UpgradeSkills": {"100902111": [{"ConditionId": 10090201, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 10090202, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10090203, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10090204, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201182, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202461, "Rank": 5, "UpgradeSkills": {"100902211": [{"ConditionId": 10090205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10090206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "101001": [{"SkillId": 200362, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201052, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200682, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201051, "Rank": 3, "UpgradeSkills": {"101001111": [{"ConditionId": 10100101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10100102, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}, {"SkillId": 201032, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200681, "Rank": 5, "UpgradeSkills": {"101001211": [{"ConditionId": 10100103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10100104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10100105, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}]}}], "101002": [{"SkillId": 200162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200682, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201902, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201042, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200681, "Rank": 3, "UpgradeSkills": {"101002111": [{"ConditionId": 10100201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201322, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201901, "Rank": 5, "UpgradeSkills": {"101002211": [{"ConditionId": 10100202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10100203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10100204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "101101": [{"SkillId": 200871, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200512, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201151, "Rank": 3, "UpgradeSkills": {"101101111": [{"ConditionId": 10110101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10110102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 200592, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200601, "Rank": 5, "UpgradeSkills": {"101101211": [{"ConditionId": 10110103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10110104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10110105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "101102": [{"SkillId": 200472, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200742, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201422, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200771, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200741, "Rank": 3, "UpgradeSkills": {"101102111": [{"ConditionId": 10110201, "Type": 5, "Group": 0, "Requirement": 4, "AdditionalRequirement": 0}, {"ConditionId": 10110202, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10110203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201542, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201421, "Rank": 5, "UpgradeSkills": {"101102211": [{"ConditionId": 10110204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10110205, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10110206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "101103": [{"SkillId": 202152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200202, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203372, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201382, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202151, "Rank": 3, "UpgradeSkills": {"101103111": [{"ConditionId": 10110301, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10110302, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10110303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201392, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203371, "Rank": 5, "UpgradeSkills": {"101103211": [{"ConditionId": 10110304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10110305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10110306, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10110307, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}], "101201": [{"SkillId": 200502, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200632, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200732, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201452, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200731, "Rank": 3, "UpgradeSkills": {"101201111": [{"ConditionId": 10120101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120103, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}], "101201121": [{"ConditionId": 10120105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120107, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201462, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200631, "Rank": 5, "UpgradeSkills": {"101201211": [{"ConditionId": 10120104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "101202": [{"SkillId": 202022, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200632, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202021, "Rank": 3, "UpgradeSkills": {"101202111": [{"ConditionId": 10120201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120202, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 2}, {"ConditionId": 10120203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200642, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202491, "Rank": 5, "UpgradeSkills": {"101202211": [{"ConditionId": 10120205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120207, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "101202221": [{"ConditionId": 10120208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120210, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10120211, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}], "101301": [{"SkillId": 200172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200562, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201192, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201182, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201191, "Rank": 3, "UpgradeSkills": {"101301111": [{"ConditionId": 10130101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10130102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10130103, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}, {"SkillId": 201352, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200561, "Rank": 5, "UpgradeSkills": {"101301211": [{"ConditionId": 10130104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10130105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10130106, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}], "101302": [{"SkillId": 200362, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200562, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200742, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200192, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200741, "Rank": 3, "UpgradeSkills": {"101302111": [{"ConditionId": 10130201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10130202, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}, {"SkillId": 200462, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200361, "Rank": 5, "UpgradeSkills": {"101302211": [{"ConditionId": 10130203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10130204, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "101303": [{"SkillId": 200432, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202192, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202011, "Rank": 3, "UpgradeSkills": {"101303111": [{"ConditionId": 10130301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10130302, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10130303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200562, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202191, "Rank": 5, "UpgradeSkills": {"101303211": [{"ConditionId": 10130304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10130305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10130306, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "101401": [{"SkillId": 200362, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201122, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201312, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200562, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201121, "Rank": 3, "UpgradeSkills": {"101401111": [{"ConditionId": 10140101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10140102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200722, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200561, "Rank": 5, "UpgradeSkills": {"101401211": [{"ConditionId": 10140103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10140104, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}], "101402": [{"SkillId": 200512, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200612, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200701, "Rank": 3, "UpgradeSkills": {"101402111": [{"ConditionId": 10140201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10140202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10140203, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}], "101402121": [{"ConditionId": 10140206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10140207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10140208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201412, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200611, "Rank": 5, "UpgradeSkills": {"101402211": [{"ConditionId": 10140204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10140205, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}], "101501": [{"SkillId": 200582, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200722, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200142, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200362, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200581, "Rank": 3, "UpgradeSkills": {"101501111": [{"ConditionId": 10150101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10150102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10150103, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}, {"SkillId": 200562, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200721, "Rank": 5, "UpgradeSkills": {"101501211": [{"ConditionId": 10150104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10150105, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}], "101502": [{"SkillId": 201312, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200252, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200351, "Rank": 3, "UpgradeSkills": {"101502111": [{"ConditionId": 10150201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10150202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10150203, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "101502121": [{"ConditionId": 10150207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10150208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10150209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200202, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202011, "Rank": 5, "UpgradeSkills": {"101502211": [{"ConditionId": 10150204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10150205, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10150206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "101601": [{"SkillId": 200362, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200512, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200361, "Rank": 3, "UpgradeSkills": {"101601111": [{"ConditionId": 10160101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10160102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10160103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201641, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200511, "Rank": 5, "UpgradeSkills": {"101601211": [{"ConditionId": 10160104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10160105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10160106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "101602": [{"SkillId": 200172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202472, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202482, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200742, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202471, "Rank": 3, "UpgradeSkills": {"101602111": [{"ConditionId": 10160201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10160202, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10160203, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10160204, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}, {"SkillId": 202192, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202481, "Rank": 5, "UpgradeSkills": {"101602211": [{"ConditionId": 10160205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10160206, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10160207, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "101701": [{"SkillId": 200332, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200891, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201151, "Rank": 3, "UpgradeSkills": {"101701111": [{"ConditionId": 10170101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10170102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10170103, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10170104, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201322, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200331, "Rank": 5, "UpgradeSkills": {"101701211": [{"ConditionId": 10170105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10170106, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10170107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10170108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "101702": [{"SkillId": 200192, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200752, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200562, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201342, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200561, "Rank": 3, "UpgradeSkills": {"101702111": [{"ConditionId": 10170201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10170202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10170203, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}, {"SkillId": 201312, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200194, "Rank": 5, "UpgradeSkills": {"101702211": [{"ConditionId": 10170204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10170205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10170206, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "101801": [{"SkillId": 200502, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200722, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201372, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200941, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201371, "Rank": 3, "UpgradeSkills": {"101801111": [{"ConditionId": 10180101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10180102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10180103, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}, {"SkillId": 200931, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200501, "Rank": 5, "UpgradeSkills": {"101801211": [{"ConditionId": 10180104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10180105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10180106, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "101802": [{"SkillId": 200342, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201322, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201532, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201071, "Rank": 3, "UpgradeSkills": {"101802111": [{"ConditionId": 10180201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10180202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10180203, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}]}}, {"SkillId": 200462, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200341, "Rank": 5, "UpgradeSkills": {"101802211": [{"ConditionId": 10180204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10180205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10180206, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "101901": [{"SkillId": 201591, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201682, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201382, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200162, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201592, "Rank": 3, "UpgradeSkills": {"101901111": [{"ConditionId": 10190101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201102, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201681, "Rank": 5, "UpgradeSkills": {"101901211": [{"ConditionId": 10190104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190108, "Type": 1, "Group": 3, "Requirement": 9, "AdditionalRequirement": 2}], "101901221": [{"ConditionId": 10190109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190110, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190111, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}], "101902": [{"SkillId": 202272, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201591, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200461, "Rank": 3, "UpgradeSkills": {"101902111": [{"ConditionId": 10190201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190202, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10190203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "101902121": [{"ConditionId": 10190207, "Type": 1, "Group": 1, "Requirement": 9, "AdditionalRequirement": 2}, {"ConditionId": 10190208, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10190209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201682, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202271, "Rank": 5, "UpgradeSkills": {"101902211": [{"ConditionId": 10190204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10190206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "102001": [{"SkillId": 200881, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201192, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201522, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201191, "Rank": 3, "UpgradeSkills": {"102001111": [{"ConditionId": 10200101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10200102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10200103, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10200104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "102001121": [{"ConditionId": 10200109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10200110, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10200111, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}, {"SkillId": 201611, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200541, "Rank": 5, "UpgradeSkills": {"102001211": [{"ConditionId": 10200105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10200106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10200107, "Type": 1, "Group": 2, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 10200108, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "102002": [{"SkillId": 200172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200712, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201272, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200541, "Rank": 3, "UpgradeSkills": {"102002111": [{"ConditionId": 10200201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10200202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10200203, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "102002121": [{"ConditionId": 10200208, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 4}, {"ConditionId": 10200209, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 200532, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201271, "Rank": 5, "UpgradeSkills": {"102002211": [{"ConditionId": 10200204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10200205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10200206, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10200207, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}], "102101": [{"SkillId": 201611, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201132, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200592, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200591, "Rank": 3, "UpgradeSkills": {"102101111": [{"ConditionId": 10210101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10210102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10210103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "102101121": [{"ConditionId": 10210107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10210108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200642, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201612, "Rank": 5, "UpgradeSkills": {"102101211": [{"ConditionId": 10210104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10210105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10210106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "102102": [{"SkillId": 200462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200722, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201902, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200162, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200721, "Rank": 3, "UpgradeSkills": {"102102111": [{"ConditionId": 10210201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10210202, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}, {"SkillId": 201611, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200461, "Rank": 5, "UpgradeSkills": {"102102211": [{"ConditionId": 10210203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10210204, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}], "102102221": [{"ConditionId": 10210205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10210206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "102201": [{"SkillId": 200192, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200582, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201342, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200581, "Rank": 3, "UpgradeSkills": {"102201111": [{"ConditionId": 10220101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10220102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10220103, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}, {"SkillId": 200012, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201341, "Rank": 5, "UpgradeSkills": {"102201211": [{"ConditionId": 10220104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10220105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10220106, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "102202": [{"SkillId": 200152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201902, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201052, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201042, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201051, "Rank": 3, "UpgradeSkills": {"102202111": [{"ConditionId": 10220201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10220202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10220203, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}]}}, {"SkillId": 200192, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201901, "Rank": 5, "UpgradeSkills": {"102202211": [{"ConditionId": 10220204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10220205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10220206, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "102202221": [{"ConditionId": 10220207, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10220208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10220209, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "102301": [{"SkillId": 200162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200742, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200562, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200262, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200561, "Rank": 3, "UpgradeSkills": {"102301111": [{"ConditionId": 10230101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10230102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10230103, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 200821, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200741, "Rank": 5, "UpgradeSkills": {"102301211": [{"ConditionId": 10230104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10230105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10230106, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}], "102302": [{"SkillId": 200512, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201202, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201532, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201201, "Rank": 3, "UpgradeSkills": {"102302111": [{"ConditionId": 10230201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10230202, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201312, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200511, "Rank": 5, "UpgradeSkills": {"102302211": [{"ConditionId": 10230203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10230204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "102303": [{"SkillId": 202772, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202582, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200012, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202771, "Rank": 3, "UpgradeSkills": {"102303111": [{"ConditionId": 10230301, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10230302, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10230303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 203132, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202581, "Rank": 5, "UpgradeSkills": {"102303211": [{"ConditionId": 10230304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10230305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10230306, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "102401": [{"SkillId": 200382, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201272, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200742, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200492, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200381, "Rank": 3, "UpgradeSkills": {"102401111": [{"ConditionId": 10240101, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}, {"ConditionId": 10240102, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 4}]}}, {"SkillId": 200502, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200491, "Rank": 5, "UpgradeSkills": {"102401211": [{"ConditionId": 10240103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10240104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10240105, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "102402": [{"SkillId": 200352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201332, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200432, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201161, "Rank": 3, "UpgradeSkills": {"102402111": [{"ConditionId": 10240201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10240202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200332, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200351, "Rank": 5, "UpgradeSkills": {"102402211": [{"ConditionId": 10240203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10240204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10240205, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10240206, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}], "102403": [{"SkillId": 201262, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202822, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203202, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201601, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202821, "Rank": 3, "UpgradeSkills": {"102403111": [{"ConditionId": 10240301, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10240302, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10240303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 203212, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203201, "Rank": 5, "UpgradeSkills": {"102403211": [{"ConditionId": 10240304, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 10240305, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10240306, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "102501": [{"SkillId": 200232, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201222, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201422, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201581, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201421, "Rank": 3, "UpgradeSkills": {"102501111": [{"ConditionId": 10250101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10250102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201202, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201221, "Rank": 5, "UpgradeSkills": {"102501211": [{"ConditionId": 10250103, "Type": 5, "Group": 0, "Requirement": 4, "AdditionalRequirement": 0}, {"ConditionId": 10250104, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 4}]}}], "102502": [{"SkillId": 200642, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200742, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202852, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200142, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200641, "Rank": 3, "UpgradeSkills": {"102502111": [{"ConditionId": 10250201, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 2}, {"ConditionId": 10250202, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10250203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202562, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202851, "Rank": 5, "UpgradeSkills": {"102502211": [{"ConditionId": 10250204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10250205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10250206, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}], "102601": [{"SkillId": 200232, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200712, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201242, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200531, "Rank": 3, "UpgradeSkills": {"102601111": [{"ConditionId": 10260101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10260102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10260103, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201252, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200711, "Rank": 5, "UpgradeSkills": {"102601211": [{"ConditionId": 10260104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10260105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10260106, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "102602": [{"SkillId": 200432, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200762, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201522, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200541, "Rank": 3, "UpgradeSkills": {"102602111": [{"ConditionId": 10260201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10260202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10260203, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "102602121": [{"ConditionId": 10260207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10260208, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10260209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201601, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200431, "Rank": 5, "UpgradeSkills": {"102602211": [{"ConditionId": 10260204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10260205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10260206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "102701": [{"SkillId": 200232, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200722, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200612, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200472, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200721, "Rank": 3, "UpgradeSkills": {"102701111": [{"ConditionId": 10270101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10270102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201102, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200611, "Rank": 5, "UpgradeSkills": {"102701211": [{"ConditionId": 10270103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10270104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10270105, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}], "102702": [{"SkillId": 202172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201342, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200292, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201341, "Rank": 3, "UpgradeSkills": {"102702111": [{"ConditionId": 10270201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10270202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10270203, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}, {"SkillId": 201322, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202171, "Rank": 5, "UpgradeSkills": {"102702211": [{"ConditionId": 10270204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10270205, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}], "102702221": [{"ConditionId": 10270206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10270207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10270208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "102801": [{"SkillId": 200212, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201002, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201362, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201001, "Rank": 3, "UpgradeSkills": {"102801111": [{"ConditionId": 10280101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10280102, "Type": 1, "Group": 2, "Requirement": 5, "AdditionalRequirement": 2}]}}, {"SkillId": 201012, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201351, "Rank": 5, "UpgradeSkills": {"102801211": [{"ConditionId": 10280103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10280104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10280105, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}], "102801221": [{"ConditionId": 10280106, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10280107, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10280108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "102901": [{"SkillId": 200492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201342, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200242, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200491, "Rank": 3, "UpgradeSkills": {"102901111": [{"ConditionId": 10290101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10290102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10290103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10290104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10290105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10290106, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201322, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201341, "Rank": 5, "UpgradeSkills": {"102901211": [{"ConditionId": 10290107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10290108, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}], "102901221": [{"ConditionId": 10290109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10290110, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "102902": [{"SkillId": 202372, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200342, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200282, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202371, "Rank": 3, "UpgradeSkills": {"102902111": [{"ConditionId": 10290201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10290202, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}, {"SkillId": 201042, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200341, "Rank": 5, "UpgradeSkills": {"102902211": [{"ConditionId": 10290203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10290204, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10290205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "102902221": [{"ConditionId": 10290206, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10290207, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10290208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103001": [{"SkillId": 200861, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201342, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200742, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201581, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201341, "Rank": 3, "UpgradeSkills": {"103001111": [{"ConditionId": 10300101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10300102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201352, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200741, "Rank": 5, "UpgradeSkills": {"103001211": [{"ConditionId": 10300103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10300104, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10300105, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "103002": [{"SkillId": 200771, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200562, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200352, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200561, "Rank": 3, "UpgradeSkills": {"103002111": [{"ConditionId": 10300201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10300202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10300203, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 200851, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200772, "Rank": 5, "UpgradeSkills": {"103002211": [{"ConditionId": 10300204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10300205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103003": [{"SkillId": 201581, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200062, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201172, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201582, "Rank": 3, "UpgradeSkills": {"103003111": [{"ConditionId": 10300301, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10300302, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10300303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202482, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200064, "Rank": 5, "UpgradeSkills": {"103003211": [{"ConditionId": 10300304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10300305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103101": [{"SkillId": 201651, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201282, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202132, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200032, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201281, "Rank": 3, "UpgradeSkills": {"103101111": [{"ConditionId": 10310101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10310102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10310103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201082, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202131, "Rank": 5, "UpgradeSkills": {"103101211": [{"ConditionId": 10310104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10310105, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}], "103102": [{"SkillId": 201601, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200432, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201252, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201522, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200431, "Rank": 3, "UpgradeSkills": {"103102111": [{"ConditionId": 10310201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10310202, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 4}]}}, {"SkillId": 201082, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201253, "Rank": 5, "UpgradeSkills": {"103102211": [{"ConditionId": 10310203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10310204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10310205, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "103103": [{"SkillId": 202692, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201661, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203462, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202691, "Rank": 3, "UpgradeSkills": {"103103111": [{"ConditionId": 10310301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10310302, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}]}}, {"SkillId": 203402, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201662, "Rank": 5, "UpgradeSkills": {"103103211": [{"ConditionId": 10310303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10310304, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10310305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103201": [{"SkillId": 200132, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201151, "Rank": 3, "UpgradeSkills": {"103201111": [{"ConditionId": 10320101, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}, {"ConditionId": 10320102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 201112, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200571, "Rank": 5, "UpgradeSkills": {"103201211": [{"ConditionId": 10320103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10320104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10320105, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}], "103202": [{"SkillId": 202702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202372, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200182, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202701, "Rank": 3, "UpgradeSkills": {"103202111": [{"ConditionId": 10320201, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10320202, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10320203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201902, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202371, "Rank": 5, "UpgradeSkills": {"103202211": [{"ConditionId": 10320204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10320205, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10320206, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "103203": [{"SkillId": 202982, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201591, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202652, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201322, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202981, "Rank": 3, "UpgradeSkills": {"103203111": [{"ConditionId": 10320301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10320302, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 4}]}}, {"SkillId": 200492, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201592, "Rank": 5, "UpgradeSkills": {"103203211": [{"ConditionId": 10320303, "Type": 3, "Group": 0, "Requirement": 4, "AdditionalRequirement": 0}, {"ConditionId": 10320304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10320305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103301": [{"SkillId": 200492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200632, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201491, "Rank": 3, "UpgradeSkills": {"103301111": [{"ConditionId": 10330101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10330102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10330103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 200861, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200631, "Rank": 5, "UpgradeSkills": {"103301211": [{"ConditionId": 10330104, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 2}, {"ConditionId": 10330105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103302": [{"SkillId": 202872, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203342, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 203341, "Rank": 3, "UpgradeSkills": {"103302111": [{"ConditionId": 10330201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10330202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 203152, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203171, "Rank": 5, "UpgradeSkills": {"103302211": [{"ConditionId": 10330203, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10330204, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10330205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103401": [{"SkillId": 201611, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202002, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200202, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201612, "Rank": 3, "UpgradeSkills": {"103401111": [{"ConditionId": 10340101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200642, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202001, "Rank": 5, "UpgradeSkills": {"103401211": [{"ConditionId": 10340105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340106, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "103401221": [{"ConditionId": 10340107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340108, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10340109, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10340110, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103402": [{"SkillId": 200952, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200752, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200642, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201472, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200751, "Rank": 3, "UpgradeSkills": {"103402111": [{"ConditionId": 10340201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340203, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}, {"SkillId": 202322, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200641, "Rank": 5, "UpgradeSkills": {"103402211": [{"ConditionId": 10340204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340206, "Type": 1, "Group": 2, "Requirement": 4, "AdditionalRequirement": 2}], "103402221": [{"ConditionId": 10340207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340208, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10340209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10340210, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103501": [{"SkillId": 200152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200732, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201402, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200592, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201401, "Rank": 3, "UpgradeSkills": {"103501111": [{"ConditionId": 10350101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10350102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10350103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201392, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200591, "Rank": 5, "UpgradeSkills": {"103501211": [{"ConditionId": 10350104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10350105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10350106, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "103502": [{"SkillId": 202172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201412, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200592, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201411, "Rank": 3, "UpgradeSkills": {"103502111": [{"ConditionId": 10350201, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 4}, {"ConditionId": 10350202, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 202152, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201701, "Rank": 5, "UpgradeSkills": {"103502211": [{"ConditionId": 10350203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10350204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103503": [{"SkillId": 200132, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202642, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200462, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202101, "Rank": 3, "UpgradeSkills": {"103503111": [{"ConditionId": 10350301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10350302, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10350303, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}, {"SkillId": 202622, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202641, "Rank": 5, "UpgradeSkills": {"103503211": [{"ConditionId": 10350304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10350305, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}], "103601": [{"SkillId": 200462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201492, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202101, "Rank": 3, "UpgradeSkills": {"103601111": [{"ConditionId": 10360101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10360102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10360103, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201502, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201453, "Rank": 5, "UpgradeSkills": {"103601211": [{"ConditionId": 10360104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10360105, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 2}, {"ConditionId": 10360106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10360107, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "103602": [{"SkillId": 201222, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201392, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200752, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202452, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201221, "Rank": 3, "UpgradeSkills": {"103602111": [{"ConditionId": 10360201, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10360202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10360203, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}], "103602121": [{"ConditionId": 10360207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10360208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10360209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202072, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200751, "Rank": 5, "UpgradeSkills": {"103602211": [{"ConditionId": 10360204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10360205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10360206, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "103701": [{"SkillId": 200502, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200512, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200501, "Rank": 3, "UpgradeSkills": {"103701111": [{"ConditionId": 10370101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10370102, "Type": 6, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10370103, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 200432, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200511, "Rank": 5, "UpgradeSkills": {"103701211": [{"ConditionId": 10370104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10370105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10370106, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "103702": [{"SkillId": 200302, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200592, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201151, "Rank": 3, "UpgradeSkills": {"103702111": [{"ConditionId": 10370201, "Type": 4, "Group": 0, "Requirement": 4, "AdditionalRequirement": 0}, {"ConditionId": 10370202, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}], "103702121": [{"ConditionId": 10370206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10370207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10370208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10370209, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}, {"SkillId": 201392, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201103, "Rank": 5, "UpgradeSkills": {"103702211": [{"ConditionId": 10370203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10370204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10370205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103703": [{"SkillId": 201382, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200032, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202452, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200034, "Rank": 3, "UpgradeSkills": {"103703111": [{"ConditionId": 10370301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10370302, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202622, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202151, "Rank": 5, "UpgradeSkills": {"103703211": [{"ConditionId": 10370303, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10370304, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10370305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103801": [{"SkillId": 200462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201372, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201002, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201371, "Rank": 3, "UpgradeSkills": {"103801111": [{"ConditionId": 10380101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380104, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10380105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380106, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 200582, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201011, "Rank": 5, "UpgradeSkills": {"103801211": [{"ConditionId": 10380107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103802": [{"SkillId": 200851, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201322, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200652, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201011, "Rank": 3, "UpgradeSkills": {"103802111": [{"ConditionId": 10380201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380203, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}], "103802121": [{"ConditionId": 10380208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380209, "Type": 1, "Group": 2, "Requirement": 5, "AdditionalRequirement": 2}]}}, {"SkillId": 201532, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200651, "Rank": 5, "UpgradeSkills": {"103802211": [{"ConditionId": 10380204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10380207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "103901": [{"SkillId": 200492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200612, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201382, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200491, "Rank": 3, "UpgradeSkills": {"103901111": [{"ConditionId": 10390101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10390102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10390103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10390104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200132, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200611, "Rank": 5, "UpgradeSkills": {"103901211": [{"ConditionId": 10390105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10390106, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 4}]}}], "103902": [{"SkillId": 202452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202472, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202451, "Rank": 3, "UpgradeSkills": {"103902111": [{"ConditionId": 10390201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10390202, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10390203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201112, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202171, "Rank": 5, "UpgradeSkills": {"103902211": [{"ConditionId": 10390204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10390205, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10390206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10390207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "104001": [{"SkillId": 200052, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200692, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201651, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200691, "Rank": 3, "UpgradeSkills": {"104001111": [{"ConditionId": 10400101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201062, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200601, "Rank": 5, "UpgradeSkills": {"104001211": [{"ConditionId": 10400104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400106, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}], "104001221": [{"ConditionId": 10400107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400108, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10400109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "104002": [{"SkillId": 200232, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201092, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201692, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201542, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201091, "Rank": 3, "UpgradeSkills": {"104002111": [{"ConditionId": 10400201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400202, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10400203, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201392, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201691, "Rank": 5, "UpgradeSkills": {"104002211": [{"ConditionId": 10400204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400206, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "104003": [{"SkillId": 202152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201392, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203672, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202522, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202151, "Rank": 3, "UpgradeSkills": {"104003111": [{"ConditionId": 10400301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400302, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10400303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202662, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203671, "Rank": 5, "UpgradeSkills": {"104003211": [{"ConditionId": 10400304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10400306, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "104101": [{"SkillId": 200841, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201312, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200652, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200982, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200651, "Rank": 3, "UpgradeSkills": {"104101111": [{"ConditionId": 10410101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10410102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 200992, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200981, "Rank": 5, "UpgradeSkills": {"104101211": [{"ConditionId": 10410103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10410104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10410105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10410106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "104102": [{"SkillId": 202042, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200962, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201522, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202041, "Rank": 3, "UpgradeSkills": {"104102111": [{"ConditionId": 10410201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10410202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10410203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200532, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200963, "Rank": 5, "UpgradeSkills": {"104102211": [{"ConditionId": 10410204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10410205, "Type": 1, "Group": 1, "Requirement": 5, "AdditionalRequirement": 2}, {"ConditionId": 10410206, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "104201": [{"SkillId": 200152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200962, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202042, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200963, "Rank": 3, "UpgradeSkills": {"104201111": [{"ConditionId": 10420101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10420102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10420103, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201902, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201071, "Rank": 5, "UpgradeSkills": {"104201211": [{"ConditionId": 10420104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10420105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10420106, "Type": 1, "Group": 2, "Requirement": 5, "AdditionalRequirement": 2}, {"ConditionId": 10420107, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}]}}], "104202": [{"SkillId": 200372, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202722, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202472, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200371, "Rank": 3, "UpgradeSkills": {"104202111": [{"ConditionId": 10420201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10420202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10420203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202672, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202721, "Rank": 5, "UpgradeSkills": {"104202211": [{"ConditionId": 10420204, "Type": 1, "Group": 1, "Requirement": 5, "AdditionalRequirement": 2}, {"ConditionId": 10420205, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10420206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "104301": [{"SkillId": 202343, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202282, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201052, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202281, "Rank": 3, "UpgradeSkills": {"104301111": [{"ConditionId": 10430101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10430102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10430103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201072, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202341, "Rank": 5, "UpgradeSkills": {"104301211": [{"ConditionId": 10430104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10430105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10430106, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "104301221": [{"ConditionId": 10430107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10430108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10430109, "Type": 1, "Group": 2, "Requirement": 9, "AdditionalRequirement": 2}]}}], "104401": [{"SkillId": 200781, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201482, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202082, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201512, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201481, "Rank": 3, "UpgradeSkills": {"104401111": [{"ConditionId": 10440101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10440102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 200642, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201511, "Rank": 5, "UpgradeSkills": {"104401211": [{"ConditionId": 10440103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10440104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10440105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10440106, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "104401221": [{"ConditionId": 10440107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10440108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10440109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10440110, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "104402": [{"SkillId": 201651, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202922, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200632, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202491, "Rank": 3, "UpgradeSkills": {"104402111": [{"ConditionId": 10440201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10440202, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10440203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202942, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202921, "Rank": 5, "UpgradeSkills": {"104402211": [{"ConditionId": 10440204, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 2}, {"ConditionId": 10440205, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10440206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "104501": [{"SkillId": 200352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200742, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201372, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200462, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201371, "Rank": 3, "UpgradeSkills": {"104501111": [{"ConditionId": 10450101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10450102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10450103, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}, {"SkillId": 200881, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200351, "Rank": 5, "UpgradeSkills": {"104501211": [{"ConditionId": 10450104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10450105, "Type": 5, "Group": 0, "Requirement": 4, "AdditionalRequirement": 0}]}}], "104502": [{"SkillId": 200332, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201322, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200331, "Rank": 3, "UpgradeSkills": {"104502111": [{"ConditionId": 10450201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10450202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10450203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10450204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201102, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201161, "Rank": 5, "UpgradeSkills": {"104502211": [{"ConditionId": 10450205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10450206, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10450207, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "104503": [{"SkillId": 200742, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202372, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202482, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200462, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202371, "Rank": 3, "UpgradeSkills": {"104503111": [{"ConditionId": 10450301, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10450302, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201172, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202481, "Rank": 5, "UpgradeSkills": {"104503211": [{"ConditionId": 10450303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10450304, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 4}]}}], "104601": [{"SkillId": 200452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201522, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201672, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200162, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200451, "Rank": 3, "UpgradeSkills": {"104601111": [{"ConditionId": 10460101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "104601121": [{"ConditionId": 10460106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200952, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201671, "Rank": 5, "UpgradeSkills": {"104601211": [{"ConditionId": 10460103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460104, "Type": 1, "Group": 1, "Requirement": 9, "AdditionalRequirement": 2}, {"ConditionId": 10460105, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "104602": [{"SkillId": 201672, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201252, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202132, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202312, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201671, "Rank": 3, "UpgradeSkills": {"104602111": [{"ConditionId": 10460201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460203, "Type": 1, "Group": 2, "Requirement": 9, "AdditionalRequirement": 2}], "104602121": [{"ConditionId": 10460208, "Type": 1, "Group": 1, "Requirement": 9, "AdditionalRequirement": 4}, {"ConditionId": 10460209, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 202352, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202311, "Rank": 5, "UpgradeSkills": {"104602211": [{"ConditionId": 10460204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460205, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10460206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "104603": [{"SkillId": 200432, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203103, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202832, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200532, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200431, "Rank": 3, "UpgradeSkills": {"104603111": [{"ConditionId": 10460301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460302, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10460303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200452, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202831, "Rank": 5, "UpgradeSkills": {"104603211": [{"ConditionId": 10460304, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 4}, {"ConditionId": 10460305, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}], "104701": [{"SkillId": 200192, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201322, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200571, "Rank": 3, "UpgradeSkills": {"104701111": [{"ConditionId": 10470101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10470102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10470103, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10470104, "Type": 1, "Group": 3, "Requirement": 2, "AdditionalRequirement": 2}], "104701121": [{"ConditionId": 10470107, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 4}, {"ConditionId": 10470108, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 201172, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201113, "Rank": 5, "UpgradeSkills": {"104701211": [{"ConditionId": 10470105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10470106, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}]}}], "104702": [{"SkillId": 202372, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201312, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200192, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202472, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201313, "Rank": 3, "UpgradeSkills": {"104702111": [{"ConditionId": 10470201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10470202, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10470203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202772, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200194, "Rank": 5, "UpgradeSkills": {"104702211": [{"ConditionId": 10470204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10470205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10470206, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "104801": [{"SkillId": 200302, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200601, "Rank": 3, "UpgradeSkills": {"104801111": [{"ConditionId": 10480101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10480102, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10480103, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}, {"SkillId": 201702, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200571, "Rank": 5, "UpgradeSkills": {"104801211": [{"ConditionId": 10480104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10480105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10480106, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10480107, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}], "104802": [{"SkillId": 202662, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202082, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202122, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201142, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202081, "Rank": 3, "UpgradeSkills": {"104802111": [{"ConditionId": 10480201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10480202, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10480203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10480204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200302, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202121, "Rank": 5, "UpgradeSkills": {"104802211": [{"ConditionId": 10480205, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10480206, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10480207, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "104802221": [{"ConditionId": 10480208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10480209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10480210, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10480211, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "104901": [{"SkillId": 202442, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202032, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201382, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202031, "Rank": 3, "UpgradeSkills": {"104901111": [{"ConditionId": 10490101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10490102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10490103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201102, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202441, "Rank": 5, "UpgradeSkills": {"104901211": [{"ConditionId": 10490104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10490105, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10490106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "105001": [{"SkillId": 201641, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200622, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200642, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200621, "Rank": 3, "UpgradeSkills": {"105001111": [{"ConditionId": 10500101, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 4}, {"ConditionId": 10500102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 201452, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200641, "Rank": 5, "UpgradeSkills": {"105001211": [{"ConditionId": 10500103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10500104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10500105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "105002": [{"SkillId": 200492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202382, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202082, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201552, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202381, "Rank": 3, "UpgradeSkills": {"105002111": [{"ConditionId": 10500201, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 2}, {"ConditionId": 10500202, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201452, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202081, "Rank": 5, "UpgradeSkills": {"105002211": [{"ConditionId": 10500203, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10500204, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "105003": [{"SkillId": 202902, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202882, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202922, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202932, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202901, "Rank": 3, "UpgradeSkills": {"105003111": [{"ConditionId": 10500301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10500302, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201452, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202881, "Rank": 5, "UpgradeSkills": {"105003211": [{"ConditionId": 10500303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10500304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "105101": [{"SkillId": 200362, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201062, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201342, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200652, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200361, "Rank": 3, "UpgradeSkills": {"105101111": [{"ConditionId": 10510101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "105101121": [{"ConditionId": 10510105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10510106, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 201392, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201341, "Rank": 5, "UpgradeSkills": {"105101211": [{"ConditionId": 10510102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10510103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10510104, "Type": 1, "Group": 3, "Requirement": 2, "AdditionalRequirement": 2}]}}], "105102": [{"SkillId": 201042, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200052, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201043, "Rank": 3, "UpgradeSkills": {"105102111": [{"ConditionId": 10510201, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10510202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10510203, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10510204, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}, {"SkillId": 202472, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202171, "Rank": 5, "UpgradeSkills": {"105102211": [{"ConditionId": 10510205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10510206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10510207, "Type": 1, "Group": 2, "Requirement": 5, "AdditionalRequirement": 2}, {"ConditionId": 10510208, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}], "105102221": [{"ConditionId": 10510209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10510210, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10510211, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "105201": [{"SkillId": 200442, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200652, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201412, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200472, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200441, "Rank": 3, "UpgradeSkills": {"105201111": [{"ConditionId": 10520101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10520102, "Type": 6, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10520103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200302, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200471, "Rank": 5, "UpgradeSkills": {"105201211": [{"ConditionId": 10520104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10520105, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "105202": [{"SkillId": 200452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201402, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201621, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200451, "Rank": 3, "UpgradeSkills": {"105202111": [{"ConditionId": 10520201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10520202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10520203, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "105202121": [{"ConditionId": 10520206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10520207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200242, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201401, "Rank": 5, "UpgradeSkills": {"105202211": [{"ConditionId": 10520204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10520205, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "105301": [{"SkillId": 200462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200612, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200972, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200611, "Rank": 3, "UpgradeSkills": {"105301111": [{"ConditionId": 10530101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10530102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10530103, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "105301121": [{"ConditionId": 10530105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10530106, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10530107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202122, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202111, "Rank": 5, "UpgradeSkills": {"105301211": [{"ConditionId": 10530104, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 4}]}}], "105302": [{"SkillId": 200972, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200652, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202122, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200601, "Rank": 3, "UpgradeSkills": {"105302111": [{"ConditionId": 10530201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10530202, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10530203, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 200492, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200651, "Rank": 5, "UpgradeSkills": {"105302211": [{"ConditionId": 10530204, "Type": 1, "Group": 1, "Requirement": 5, "AdditionalRequirement": 2}, {"ConditionId": 10530205, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10530206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10530207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "105401": [{"SkillId": 200592, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201382, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200672, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201002, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200591, "Rank": 3, "UpgradeSkills": {"105401111": [{"ConditionId": 10540101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10540102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 200652, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200671, "Rank": 5, "UpgradeSkills": {"105401211": [{"ConditionId": 10540103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10540104, "Type": 1, "Group": 1, "Requirement": 5, "AdditionalRequirement": 2}, {"ConditionId": 10540105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10540106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "105501": [{"SkillId": 202102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202422, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202101, "Rank": 3, "UpgradeSkills": {"105501111": [{"ConditionId": 10550101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10550102, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10550103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}], "105501121": [{"ConditionId": 10550109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10550110, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}, {"SkillId": 201702, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200491, "Rank": 5, "UpgradeSkills": {"105501211": [{"ConditionId": 10550104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10550105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10550106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10550107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10550108, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "105601": [{"SkillId": 201562, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201422, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201232, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201571, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201231, "Rank": 3, "UpgradeSkills": {"105601111": [{"ConditionId": 10560101, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10560102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200781, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201561, "Rank": 5, "UpgradeSkills": {"105601211": [{"ConditionId": 10560103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10560104, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10560105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10560106, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10560107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10560108, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "105602": [{"SkillId": 200012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201212, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200752, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201211, "Rank": 3, "UpgradeSkills": {"105602111": [{"ConditionId": 10560201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10560202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10560203, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}, {"SkillId": 200062, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200751, "Rank": 5, "UpgradeSkills": {"105602211": [{"ConditionId": 10560204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10560205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10560206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "105701": [{"SkillId": 201142, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202522, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200642, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200232, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202521, "Rank": 3, "UpgradeSkills": {"105701111": [{"ConditionId": 10570101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10570102, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10570103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201591, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200641, "Rank": 5, "UpgradeSkills": {"105701211": [{"ConditionId": 10570104, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 4}, {"ConditionId": 10570105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "105702": [{"SkillId": 201591, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202522, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202972, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202932, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202521, "Rank": 3, "UpgradeSkills": {"105702111": [{"ConditionId": 10570201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10570202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10570203, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 202892, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202971, "Rank": 5, "UpgradeSkills": {"105702211": [{"ConditionId": 10570204, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 2}, {"ConditionId": 10570205, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10570206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "105801": [{"SkillId": 200472, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200582, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200142, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200581, "Rank": 3, "UpgradeSkills": {"105801111": [{"ConditionId": 10580101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10580102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10580103, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}]}}, {"SkillId": 200861, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200471, "Rank": 5, "UpgradeSkills": {"105801211": [{"ConditionId": 10580104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10580105, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}], "105802": [{"SkillId": 200352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201902, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202372, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201901, "Rank": 3, "UpgradeSkills": {"105802111": [{"ConditionId": 10580201, "Type": 4, "Group": 0, "Requirement": 4, "AdditionalRequirement": 0}], "105802121": [{"ConditionId": 10580205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10580206, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 4}]}}, {"SkillId": 200012, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200351, "Rank": 5, "UpgradeSkills": {"105802211": [{"ConditionId": 10580202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10580203, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10580204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "105901": [{"SkillId": 200062, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201442, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201142, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201382, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201441, "Rank": 3, "UpgradeSkills": {"105901111": [{"ConditionId": 10590101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10590102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10590103, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}], "105901121": [{"ConditionId": 10590106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200722, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200064, "Rank": 5, "UpgradeSkills": {"105901211": [{"ConditionId": 10590104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10590105, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "105902": [{"SkillId": 200062, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202122, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202082, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202121, "Rank": 3, "UpgradeSkills": {"105902111": [{"ConditionId": 10590201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10590202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10590203, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}], "105902121": [{"ConditionId": 10590207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10590208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10590209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202152, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202081, "Rank": 5, "UpgradeSkills": {"105902211": [{"ConditionId": 10590204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10590205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10590206, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}], "106001": [{"SkillId": 200122, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201442, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200911, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201161, "Rank": 3, "UpgradeSkills": {"106001111": [{"ConditionId": 10600101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600102, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10600103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600104, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 201422, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201441, "Rank": 5, "UpgradeSkills": {"106001211": [{"ConditionId": 10600105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600106, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10600107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600108, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "106002": [{"SkillId": 200492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202082, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201151, "Rank": 3, "UpgradeSkills": {"106002111": [{"ConditionId": 10600201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600203, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 200302, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200491, "Rank": 5, "UpgradeSkills": {"106002211": [{"ConditionId": 10600204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600206, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "106003": [{"SkillId": 201651, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201382, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201652, "Rank": 3, "UpgradeSkills": {"106003111": [{"ConditionId": 10600301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600302, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10600303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202161, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200601, "Rank": 5, "UpgradeSkills": {"106003211": [{"ConditionId": 10600304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600305, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10600306, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600307, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "106003221": [{"ConditionId": 10600308, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600309, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10600310, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10600311, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}], "106101": [{"SkillId": 200262, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200672, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201432, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201142, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200671, "Rank": 3, "UpgradeSkills": {"106101111": [{"ConditionId": 10610101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10610102, "Type": 1, "Group": 2, "Requirement": 5, "AdditionalRequirement": 2}]}}, {"SkillId": 201072, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201431, "Rank": 5, "UpgradeSkills": {"106101211": [{"ConditionId": 10610103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10610104, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "106102": [{"SkillId": 200172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200672, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200612, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200692, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200611, "Rank": 3, "UpgradeSkills": {"106102111": [{"ConditionId": 10610201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10610202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10610203, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}, {"SkillId": 201382, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200174, "Rank": 5, "UpgradeSkills": {"106102211": [{"ConditionId": 10610204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10610205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10610206, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "106103": [{"SkillId": 200702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200972, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202752, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200462, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200701, "Rank": 3, "UpgradeSkills": {"106103111": [{"ConditionId": 10610301, "Type": 1, "Group": 1, "Requirement": 5, "AdditionalRequirement": 2}, {"ConditionId": 10610302, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10610303, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200342, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202751, "Rank": 5, "UpgradeSkills": {"106103211": [{"ConditionId": 10610304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10610305, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10610306, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "106103221": [{"ConditionId": 10610307, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10610308, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "106201": [{"SkillId": 200482, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201212, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201621, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200481, "Rank": 3, "UpgradeSkills": {"106201111": [{"ConditionId": 10620101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10620102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 200512, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201211, "Rank": 5, "UpgradeSkills": {"106201211": [{"ConditionId": 10620103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10620104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10620105, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}], "106202": [{"SkillId": 200602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201692, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202082, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201691, "Rank": 3, "UpgradeSkills": {"106202111": [{"ConditionId": 10620201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10620202, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10620203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202622, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202081, "Rank": 5, "UpgradeSkills": {"106202211": [{"ConditionId": 10620204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10620205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10620206, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}], "106202221": [{"ConditionId": 10620207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10620208, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10620209, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "106301": [{"SkillId": 200572, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202161, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201702, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200571, "Rank": 3, "UpgradeSkills": {"106301111": [{"ConditionId": 10630101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10630102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10630103, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10630104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200182, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202151, "Rank": 5, "UpgradeSkills": {"106301211": [{"ConditionId": 10630105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10630106, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10630107, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10630108, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}], "106401": [{"SkillId": 201661, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201242, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201192, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200532, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201662, "Rank": 3, "UpgradeSkills": {"106401111": [{"ConditionId": 10640101, "Type": 2, "Group": 0, "Requirement": 202051, "AdditionalRequirement": 0}, {"ConditionId": 10640102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10640103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 200302, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201191, "Rank": 5, "UpgradeSkills": {"106401211": [{"ConditionId": 10640104, "Type": 2, "Group": 0, "Requirement": 202051, "AdditionalRequirement": 0}, {"ConditionId": 10640105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10640106, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}], "106401221": [{"ConditionId": 10640107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10640108, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10640109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "106402": [{"SkillId": 201172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202682, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200362, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201173, "Rank": 3, "UpgradeSkills": {"106402111": [{"ConditionId": 10640201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10640202, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10640203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201601, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202681, "Rank": 5, "UpgradeSkills": {"106402211": [{"ConditionId": 10640204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10640205, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10640206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "106501": [{"SkillId": 202502, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200302, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201052, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201032, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201051, "Rank": 3, "UpgradeSkills": {"106501111": [{"ConditionId": 10650101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10650102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10650103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10650104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201651, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202501, "Rank": 5, "UpgradeSkills": {"106501211": [{"ConditionId": 10650105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10650106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10650107, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}]}}], "106502": [{"SkillId": 200532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200682, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200282, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201242, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200531, "Rank": 3, "UpgradeSkills": {"106502111": [{"ConditionId": 10650201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10650202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10650203, "Type": 1, "Group": 2, "Requirement": 1, "AdditionalRequirement": 2}]}}, {"SkillId": 201601, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200681, "Rank": 5, "UpgradeSkills": {"106502211": [{"ConditionId": 10650204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10650205, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}], "106601": [{"SkillId": 202392, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201282, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201252, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202391, "Rank": 3, "UpgradeSkills": {"106601111": [{"ConditionId": 10660101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10660102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10660103, "Type": 2, "Group": 0, "Requirement": 202051, "AdditionalRequirement": 0}, {"ConditionId": 10660104, "Type": 1, "Group": 2, "Requirement": 1, "AdditionalRequirement": 2}]}}, {"SkillId": 200432, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200531, "Rank": 5, "UpgradeSkills": {"106601211": [{"ConditionId": 10660105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10660106, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "106701": [{"SkillId": 200012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201692, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202012, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201691, "Rank": 3, "UpgradeSkills": {"106701111": [{"ConditionId": 10670101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}], "106701121": [{"ConditionId": 10670107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670108, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10670109, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10670110, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200152, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200014, "Rank": 5, "UpgradeSkills": {"106701211": [{"ConditionId": 10670104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670106, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "106702": [{"SkillId": 202422, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201222, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202122, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201382, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202421, "Rank": 3, "UpgradeSkills": {"106702111": [{"ConditionId": 10670201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670203, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}, {"SkillId": 202452, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201221, "Rank": 5, "UpgradeSkills": {"106702211": [{"ConditionId": 10670204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670205, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10670206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "106703": [{"SkillId": 202452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202082, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200012, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202081, "Rank": 3, "UpgradeSkills": {"106703111": [{"ConditionId": 10670301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670302, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670303, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 200612, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201113, "Rank": 5, "UpgradeSkills": {"106703211": [{"ConditionId": 10670304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670305, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10670306, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10670307, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "106801": [{"SkillId": 200432, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200531, "Rank": 3, "UpgradeSkills": {"106801111": [{"ConditionId": 10680101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10680102, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 10680103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201272, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201173, "Rank": 5, "UpgradeSkills": {"106801211": [{"ConditionId": 10680104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10680105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10680106, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}], "106802": [{"SkillId": 201262, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200712, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200541, "Rank": 3, "UpgradeSkills": {"106802111": [{"ConditionId": 10680201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10680202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10680203, "Type": 1, "Group": 2, "Requirement": 1, "AdditionalRequirement": 4}]}}, {"SkillId": 201601, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200531, "Rank": 5, "UpgradeSkills": {"106802211": [{"ConditionId": 10680204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10680205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10680206, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "106803": [{"SkillId": 200532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202812, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202822, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201611, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202811, "Rank": 3, "UpgradeSkills": {"106803111": [{"ConditionId": 10680301, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10680302, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10680303, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}, {"SkillId": 200012, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202821, "Rank": 5, "UpgradeSkills": {"106803211": [{"ConditionId": 10680304, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10680305, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 10680306, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "106901": [{"SkillId": 200172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200582, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200342, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200581, "Rank": 3, "UpgradeSkills": {"106901111": [{"ConditionId": 10690101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10690102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}], "106901121": [{"ConditionId": 10690105, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10690106, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10690107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201532, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200174, "Rank": 5, "UpgradeSkills": {"106901211": [{"ConditionId": 10690103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10690104, "Type": 7, "Group": 0, "Requirement": 4, "AdditionalRequirement": 0}]}}], "106902": [{"SkillId": 200362, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200682, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202092, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200732, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200361, "Rank": 3, "UpgradeSkills": {"106902111": [{"ConditionId": 10690201, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10690202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201322, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202091, "Rank": 5, "UpgradeSkills": {"106902211": [{"ConditionId": 10690203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10690204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10690205, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 4}]}}], "107001": [{"SkillId": 202652, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200722, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200492, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201701, "Rank": 3, "UpgradeSkills": {"107001111": [{"ConditionId": 10700101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10700102, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10700103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201112, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200721, "Rank": 5, "UpgradeSkills": {"107001211": [{"ConditionId": 10700104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10700105, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10700106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "107001221": [{"ConditionId": 10700107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10700108, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10700109, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "107002": [{"SkillId": 200162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202662, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202472, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200164, "Rank": 3, "UpgradeSkills": {"107002111": [{"ConditionId": 10700201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10700202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10700203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202602, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202661, "Rank": 5, "UpgradeSkills": {"107002211": [{"ConditionId": 10700204, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10700205, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10700206, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}], "107101": [{"SkillId": 200022, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200572, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201322, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200571, "Rank": 3, "UpgradeSkills": {"107101111": [{"ConditionId": 10710101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10710102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 201142, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201701, "Rank": 5, "UpgradeSkills": {"107101211": [{"ConditionId": 10710103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10710104, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}]}}], "107102": [{"SkillId": 201902, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200032, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200472, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200471, "Rank": 3, "UpgradeSkills": {"107102111": [{"ConditionId": 10710201, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10710202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "107102121": [{"ConditionId": 10710207, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10710208, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10710209, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 201322, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201103, "Rank": 5, "UpgradeSkills": {"107102211": [{"ConditionId": 10710203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10710204, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}, {"ConditionId": 10710205, "Type": 1, "Group": 2, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10710206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "107201": [{"SkillId": 200462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200732, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201322, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202092, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200461, "Rank": 3, "UpgradeSkills": {"107201111": [{"ConditionId": 10720101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10720102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 200342, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200731, "Rank": 5, "UpgradeSkills": {"107201211": [{"ConditionId": 10720103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10720104, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}], "107201221": [{"ConditionId": 10720105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10720106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10720107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "107202": [{"SkillId": 200032, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202712, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202472, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201611, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202711, "Rank": 3, "UpgradeSkills": {"107202111": [{"ConditionId": 10720201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10720202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10720203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201651, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202471, "Rank": 5, "UpgradeSkills": {"107202211": [{"ConditionId": 10720204, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 4}, {"ConditionId": 10720205, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 4}]}}], "107301": [{"SkillId": 201312, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201322, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201323, "Rank": 3, "UpgradeSkills": {"107301111": [{"ConditionId": 10730101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10730102, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10730103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 203432, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201113, "Rank": 5, "UpgradeSkills": {"107301211": [{"ConditionId": 10730104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10730105, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10730106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "107401": [{"SkillId": 200472, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202072, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201212, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200471, "Rank": 3, "UpgradeSkills": {"107401111": [{"ConditionId": 10740101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10740102, "Type": 5, "Group": 0, "Requirement": 4, "AdditionalRequirement": 0}]}}, {"SkillId": 201222, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202071, "Rank": 5, "UpgradeSkills": {"107401211": [{"ConditionId": 10740103, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10740104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10740105, "Type": 1, "Group": 3, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10740106, "Type": 1, "Group": 3, "Requirement": 4, "AdditionalRequirement": 2}]}}], "107402": [{"SkillId": 202642, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201212, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202622, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202641, "Rank": 3, "UpgradeSkills": {"107402111": [{"ConditionId": 10740201, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 4}]}}, {"SkillId": 200202, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201211, "Rank": 5, "UpgradeSkills": {"107402211": [{"ConditionId": 10740202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10740203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10740204, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "107601": [{"SkillId": 201382, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202592, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201611, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202151, "Rank": 3, "UpgradeSkills": {"107601111": [{"ConditionId": 10760101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10760102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10760103, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}, {"SkillId": 202472, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202591, "Rank": 5, "UpgradeSkills": {"107601211": [{"ConditionId": 10760104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10760105, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10760106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10760107, "Type": 1, "Group": 3, "Requirement": 8, "AdditionalRequirement": 2}]}}], "107701": [{"SkillId": 200152, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202192, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201172, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200154, "Rank": 3, "UpgradeSkills": {"107701111": [{"ConditionId": 10770101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10770102, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10770103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10770104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202482, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202011, "Rank": 5, "UpgradeSkills": {"107701211": [{"ConditionId": 10770105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10770106, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10770107, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}]}}], "107702": [{"SkillId": 202192, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201322, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203222, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202372, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202191, "Rank": 3, "UpgradeSkills": {"107702111": [{"ConditionId": 10770201, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10770202, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10770203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201591, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203221, "Rank": 5, "UpgradeSkills": {"107702211": [{"ConditionId": 10770204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10770205, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10770206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "107801": [{"SkillId": 200302, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201052, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202412, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201042, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201051, "Rank": 3, "UpgradeSkills": {"107801111": [{"ConditionId": 10780101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10780102, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10780103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200342, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202411, "Rank": 5, "UpgradeSkills": {"107801211": [{"ConditionId": 10780104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10780105, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10780106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "107801221": [{"ConditionId": 10780107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10780108, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10780109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "107802": [{"SkillId": 202762, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202412, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203042, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201032, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202761, "Rank": 3, "UpgradeSkills": {"107802111": [{"ConditionId": 10780201, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10780202, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10780203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202672, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202411, "Rank": 5, "UpgradeSkills": {"107802211": [{"ConditionId": 10780204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10780205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "107901": [{"SkillId": 202532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203082, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203472, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202252, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202531, "Rank": 3, "UpgradeSkills": {"107901111": [{"ConditionId": 10790101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10790102, "Type": 1, "Group": 1, "Requirement": 9, "AdditionalRequirement": 3}]}}, {"SkillId": 203093, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203471, "Rank": 5, "UpgradeSkills": {"107901211": [{"ConditionId": 10790103, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 10790104, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10790105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10790106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "108001": [{"SkillId": 201032, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201601, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202952, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200452, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201602, "Rank": 3, "UpgradeSkills": {"108001111": [{"ConditionId": 10800101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10800102, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10800103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200532, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202951, "Rank": 5, "UpgradeSkills": {"108001211": [{"ConditionId": 10800104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10800105, "Type": 1, "Group": 1, "Requirement": 9, "AdditionalRequirement": 2}, {"ConditionId": 10800106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10800107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "108201": [{"SkillId": 200462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202502, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202672, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201042, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200461, "Rank": 3, "UpgradeSkills": {"108201111": [{"ConditionId": 10820101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10820102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10820103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202532, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202671, "Rank": 5, "UpgradeSkills": {"108201211": [{"ConditionId": 10820104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10820105, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10820106, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10820107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "108301": [{"SkillId": 200042, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202562, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202422, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201692, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202421, "Rank": 3, "UpgradeSkills": {"108301111": [{"ConditionId": 10830101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10830102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10830103, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}, {"SkillId": 200592, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202561, "Rank": 5, "UpgradeSkills": {"108301211": [{"ConditionId": 10830104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10830105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10830106, "Type": 1, "Group": 2, "Requirement": 8, "AdditionalRequirement": 2}, {"ConditionId": 10830107, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10830108, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "108302": [{"SkillId": 202092, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202161, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201702, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202091, "Rank": 3, "UpgradeSkills": {"108302111": [{"ConditionId": 10830201, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10830202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10830203, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 202082, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200601, "Rank": 5, "UpgradeSkills": {"108302211": [{"ConditionId": 10830204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10830205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10830206, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10830207, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}], "108401": [{"SkillId": 200362, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200632, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202652, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200032, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200631, "Rank": 3, "UpgradeSkills": {"108401111": [{"ConditionId": 10840101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10840102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10840103, "Type": 1, "Group": 2, "Requirement": 4, "AdditionalRequirement": 2}]}}, {"SkillId": 200642, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202651, "Rank": 5, "UpgradeSkills": {"108401211": [{"ConditionId": 10840104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10840105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10840106, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10840107, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}], "108402": [{"SkillId": 200702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200632, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203042, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200701, "Rank": 3, "UpgradeSkills": {"108402111": [{"ConditionId": 10840201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10840202, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}]}}, {"SkillId": 202652, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201453, "Rank": 5, "UpgradeSkills": {"108402211": [{"ConditionId": 10840203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "108501": [{"SkillId": 202542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200502, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202402, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201392, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202541, "Rank": 3, "UpgradeSkills": {"108501111": [{"ConditionId": 10850101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10850102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10850103, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10850104, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 200851, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202401, "Rank": 5, "UpgradeSkills": {"108501211": [{"ConditionId": 10850105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10850106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10850107, "Type": 1, "Group": 2, "Requirement": 5, "AdditionalRequirement": 2}, {"ConditionId": 10850108, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}]}}], "108502": [{"SkillId": 202402, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201611, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202962, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202622, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202401, "Rank": 3, "UpgradeSkills": {"108502111": [{"ConditionId": 10850201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10850202, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10850203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202612, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202961, "Rank": 5, "UpgradeSkills": {"108502211": [{"ConditionId": 10850204, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 4}, {"ConditionId": 10850205, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}], "108601": [{"SkillId": 200492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202742, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201352, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202741, "Rank": 3, "UpgradeSkills": {"108601111": [{"ConditionId": 10860101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201902, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202531, "Rank": 5, "UpgradeSkills": {"108601211": [{"ConditionId": 10860102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10860103, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}], "108602": [{"SkillId": 202372, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200682, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203422, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203222, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202371, "Rank": 3, "UpgradeSkills": {"108602111": [{"ConditionId": 10860201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10860202, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 3}, {"ConditionId": 10860203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201902, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200681, "Rank": 5, "UpgradeSkills": {"108602211": [{"ConditionId": 10860204, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10860205, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10860206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "108701": [{"SkillId": 200162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202042, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200972, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200531, "Rank": 3, "UpgradeSkills": {"108701111": [{"ConditionId": 10870101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10870102, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 10870103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201601, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202041, "Rank": 5, "UpgradeSkills": {"108701211": [{"ConditionId": 10870104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10870105, "Type": 1, "Group": 1, "Requirement": 5, "AdditionalRequirement": 4}]}}], "108702": [{"SkillId": 203202, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200452, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203482, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201012, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 203201, "Rank": 3, "UpgradeSkills": {"108702111": [{"ConditionId": 10870201, "Type": 1, "Group": 1, "Requirement": 5, "AdditionalRequirement": 2}, {"ConditionId": 10870202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202161, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203481, "Rank": 5, "UpgradeSkills": {"108702211": [{"ConditionId": 10870203, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10870204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "108801": [{"SkillId": 200592, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202082, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200162, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200591, "Rank": 3, "UpgradeSkills": {"108801111": [{"ConditionId": 10880101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10880102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10880103, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10880104, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}, {"SkillId": 202652, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202081, "Rank": 5, "UpgradeSkills": {"108801211": [{"ConditionId": 10880105, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 10880106, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10880107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10880108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "108901": [{"SkillId": 202432, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200512, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201352, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201651, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202431, "Rank": 3, "UpgradeSkills": {"108901111": [{"ConditionId": 10890101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10890102, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}]}}, {"SkillId": 201611, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 200511, "Rank": 5, "UpgradeSkills": {"108901211": [{"ConditionId": 10890103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10890104, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10890105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10890106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "109001": [{"SkillId": 201601, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203162, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202722, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202672, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202721, "Rank": 3, "UpgradeSkills": {"109001111": [{"ConditionId": 10900101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10900102, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10900103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202502, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201602, "Rank": 5, "UpgradeSkills": {"109001211": [{"ConditionId": 10900104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10900105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10900106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "109101": [{"SkillId": 202172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202122, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201442, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202171, "Rank": 3, "UpgradeSkills": {"109101111": [{"ConditionId": 10910101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10910102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10910103, "Type": 1, "Group": 2, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 10910104, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}, {"SkillId": 202652, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202121, "Rank": 5, "UpgradeSkills": {"109101211": [{"ConditionId": 10910105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10910106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10910107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10910108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "109301": [{"SkillId": 200582, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200992, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201902, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201312, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200581, "Rank": 3, "UpgradeSkills": {"109301111": [{"ConditionId": 10930101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10930102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10930103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202042, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201901, "Rank": 5, "UpgradeSkills": {"109301211": [{"ConditionId": 10930104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10930105, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10930106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "109401": [{"SkillId": 200022, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202622, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202662, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201382, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202621, "Rank": 3, "UpgradeSkills": {"109401111": [{"ConditionId": 10940101, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10940102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10940103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202602, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202661, "Rank": 5, "UpgradeSkills": {"109401211": [{"ConditionId": 10940104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10940105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10940106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10940107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "109401221": [{"ConditionId": 10940108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10940109, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}]}}], "109601": [{"SkillId": 202092, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202122, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203332, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202091, "Rank": 3, "UpgradeSkills": {"109601111": [{"ConditionId": 10960101, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}]}}, {"SkillId": 202082, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202121, "Rank": 5, "UpgradeSkills": {"109601211": [{"ConditionId": 10960102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10960103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10960104, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 10960105, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "109801": [{"SkillId": 202262, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201112, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202332, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202261, "Rank": 3, "UpgradeSkills": {"109801111": [{"ConditionId": 10980101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10980102, "Type": 1, "Group": 1, "Requirement": 9, "AdditionalRequirement": 4}]}}, {"SkillId": 202252, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202331, "Rank": 5, "UpgradeSkills": {"109801211": [{"ConditionId": 10980103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10980104, "Type": 7, "Group": 0, "Requirement": 4, "AdditionalRequirement": 0}]}}], "109802": [{"SkillId": 202252, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203612, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202952, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203093, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 203611, "Rank": 3, "UpgradeSkills": {"109802111": [{"ConditionId": 10980201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10980202, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10980203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202652, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202254, "Rank": 5, "UpgradeSkills": {"109802211": [{"ConditionId": 10980204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10980205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "109901": [{"SkillId": 202202, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201672, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200562, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201332, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200561, "Rank": 3, "UpgradeSkills": {"109901111": [{"ConditionId": 10990101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10990102, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 10990103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200861, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201671, "Rank": 5, "UpgradeSkills": {"109901211": [{"ConditionId": 10990104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10990105, "Type": 1, "Group": 1, "Requirement": 9, "AdditionalRequirement": 4}], "109901221": [{"ConditionId": 10990106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10990107, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10990108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "109902": [{"SkillId": 202832, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202302, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203082, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203093, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202831, "Rank": 3, "UpgradeSkills": {"109902111": [{"ConditionId": 10990201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10990202, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 10990203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202332, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203081, "Rank": 5, "UpgradeSkills": {"109902211": [{"ConditionId": 10990204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 10990205, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 10990206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "110001": [{"SkillId": 202302, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201072, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201032, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201071, "Rank": 3, "UpgradeSkills": {"110001111": [{"ConditionId": 11000101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11000102, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 11000103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "110001121": [{"ConditionId": 11000106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11000107, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 11000108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202332, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202301, "Rank": 5, "UpgradeSkills": {"110001211": [{"ConditionId": 11000104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11000105, "Type": 1, "Group": 1, "Requirement": 9, "AdditionalRequirement": 4}]}}], "110201": [{"SkillId": 200382, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201661, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201651, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200381, "Rank": 3, "UpgradeSkills": {"110201111": [{"ConditionId": 11020101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11020102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11020103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11020104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201591, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202541, "Rank": 5, "UpgradeSkills": {"110201211": [{"ConditionId": 11020105, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 11020106, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 11020107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "110401": [{"SkillId": 200032, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200552, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200531, "Rank": 3, "UpgradeSkills": {"110401111": [{"ConditionId": 11040101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11040102, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 11040103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 200732, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202461, "Rank": 5, "UpgradeSkills": {"110401211": [{"ConditionId": 11040104, "Type": 2, "Group": 0, "Requirement": 202051, "AdditionalRequirement": 0}, {"ConditionId": 11040105, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 11040106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11040107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "110402": [{"SkillId": 203382, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203392, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203402, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202602, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 203381, "Rank": 3, "UpgradeSkills": {"110402111": [{"ConditionId": 11040201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11040202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11040203, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}, {"SkillId": 202392, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203391, "Rank": 5, "UpgradeSkills": {"110402211": [{"ConditionId": 11040204, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 3}, {"ConditionId": 11040205, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 3}, {"ConditionId": 11040206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "110501": [{"SkillId": 201702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201651, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201701, "Rank": 3, "UpgradeSkills": {"110501111": [{"ConditionId": 11050101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11050102, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 11050103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202161, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202601, "Rank": 5, "UpgradeSkills": {"110501211": [{"ConditionId": 11050104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11050105, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 11050106, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}]}}], "110502": [{"SkillId": 202082, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202602, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201631, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200132, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202601, "Rank": 3, "UpgradeSkills": {"110502111": [{"ConditionId": 11050201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11050202, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11050203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202532, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201632, "Rank": 5, "UpgradeSkills": {"110502211": [{"ConditionId": 11050204, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11050205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11050206, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}]}}], "110601": [{"SkillId": 200302, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202642, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201422, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200062, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201421, "Rank": 3, "UpgradeSkills": {"110601111": [{"ConditionId": 11060101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11060102, "Type": 5, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 11060103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11060104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "110601121": [{"ConditionId": 11060109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11060110, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11060111, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11060112, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201382, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202641, "Rank": 5, "UpgradeSkills": {"110601211": [{"ConditionId": 11060105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11060106, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 11060107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11060108, "Type": 1, "Group": 2, "Requirement": 3, "AdditionalRequirement": 2}]}}], "110701": [{"SkillId": 202552, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201611, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200722, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200541, "Rank": 3, "UpgradeSkills": {"110701111": [{"ConditionId": 11070101, "Type": 2, "Group": 0, "Requirement": 202051, "AdditionalRequirement": 0}, {"ConditionId": 11070102, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 11070103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202602, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201612, "Rank": 5, "UpgradeSkills": {"110701211": [{"ConditionId": 11070104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11070105, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 11070106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11070107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "110702": [{"SkillId": 202462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202692, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202392, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202461, "Rank": 3, "UpgradeSkills": {"110702111": [{"ConditionId": 11070201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11070202, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 203392, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202391, "Rank": 5, "UpgradeSkills": {"110702211": [{"ConditionId": 11070203, "Type": 2, "Group": 0, "Requirement": 202051, "AdditionalRequirement": 0}, {"ConditionId": 11070204, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}]}}], "110801": [{"SkillId": 202652, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202722, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202872, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201462, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202721, "Rank": 3, "UpgradeSkills": {"110801111": [{"ConditionId": 11080101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11080102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 11080103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11080104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202882, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202871, "Rank": 5, "UpgradeSkills": {"110801211": [{"ConditionId": 11080105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "110901": [{"SkillId": 201902, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200582, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202502, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201312, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202501, "Rank": 3, "UpgradeSkills": {"110901111": [{"ConditionId": 11090101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11090102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 11090103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11090104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11090105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11090106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202802, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201901, "Rank": 5, "UpgradeSkills": {"110901211": [{"ConditionId": 11090107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11090108, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 4}]}}], "111001": [{"SkillId": 201702, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203172, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202161, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 201701, "Rank": 3, "UpgradeSkills": {"111001111": [{"ConditionId": 11100101, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 11100102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11100103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201412, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203171, "Rank": 5, "UpgradeSkills": {"111001211": [{"ConditionId": 11100104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11100105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11100106, "Type": 1, "Group": 2, "Requirement": 7, "AdditionalRequirement": 2}]}}], "111002": [{"SkillId": 202432, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203422, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203052, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203122, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202431, "Rank": 3, "UpgradeSkills": {"111002111": [{"ConditionId": 11100201, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11100202, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 201702, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203051, "Rank": 5, "UpgradeSkills": {"111002211": [{"ConditionId": 11100203, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11100204, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 11100205, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11100206, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "111101": [{"SkillId": 202102, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203332, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201102, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202101, "Rank": 3, "UpgradeSkills": {"111101111": [{"ConditionId": 11110101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11110102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 203322, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203331, "Rank": 5, "UpgradeSkills": {"111101211": [{"ConditionId": 11110103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11110104, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 2}, {"ConditionId": 11110105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11110106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "111501": [{"SkillId": 203532, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202872, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203512, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203522, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202871, "Rank": 3, "UpgradeSkills": {"111501111": [{"ConditionId": 11150101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11150102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11150103, "Type": 1, "Group": 2, "Requirement": 4, "AdditionalRequirement": 2}]}}, {"SkillId": 201462, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203511, "Rank": 5, "UpgradeSkills": {"111501211": [{"ConditionId": 11150104, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 4}, {"ConditionId": 11150105, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}], "111501221": [{"ConditionId": 11150106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11150107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11150108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11150109, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "111601": [{"SkillId": 200492, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201322, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203122, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202982, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200491, "Rank": 3, "UpgradeSkills": {"111601111": [{"ConditionId": 11160101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11160102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11160103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 203132, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203121, "Rank": 5, "UpgradeSkills": {"111601211": [{"ConditionId": 11160104, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 11160105, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 11160106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11160107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}], "111601221": [{"ConditionId": 11160108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11160109, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}], "111701": [{"SkillId": 202932, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201512, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202722, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200292, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202931, "Rank": 3, "UpgradeSkills": {"111701111": [{"ConditionId": 11170101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11170102, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11170103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202902, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202721, "Rank": 5, "UpgradeSkills": {"111701211": [{"ConditionId": 11170104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11170105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11170106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "111901": [{"SkillId": 200012, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202932, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203002, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200492, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200014, "Rank": 3, "UpgradeSkills": {"111901111": [{"ConditionId": 11190101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11190102, "Type": 7, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 11190103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11190104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 202852, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203001, "Rank": 5, "UpgradeSkills": {"111901211": [{"ConditionId": 11190105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "112001": [{"SkillId": 203061, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200962, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201242, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203071, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200963, "Rank": 3, "UpgradeSkills": {"112001111": [{"ConditionId": 11200101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11200102, "Type": 1, "Group": 1, "Requirement": 5, "AdditionalRequirement": 4}]}}, {"SkillId": 202862, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 201243, "Rank": 5, "UpgradeSkills": {"112001211": [{"ConditionId": 11200103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11200104, "Type": 1, "Group": 1, "Requirement": 1, "AdditionalRequirement": 2}, {"ConditionId": 11200105, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 11200106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "112101": [{"SkillId": 201462, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202922, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202402, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200192, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202921, "Rank": 3, "UpgradeSkills": {"112101111": [{"ConditionId": 11210101, "Type": 1, "Group": 1, "Requirement": 4, "AdditionalRequirement": 4}, {"ConditionId": 11210102, "Type": 3, "Group": 0, "Requirement": 5, "AdditionalRequirement": 0}]}}, {"SkillId": 202932, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202401, "Rank": 5, "UpgradeSkills": {"112101211": [{"ConditionId": 11210103, "Type": 1, "Group": 1, "Requirement": 5, "AdditionalRequirement": 2}, {"ConditionId": 11210104, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 2}, {"ConditionId": 11210105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11210106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "112401": [{"SkillId": 202802, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202412, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203132, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201661, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202411, "Rank": 3, "UpgradeSkills": {"112401111": [{"ConditionId": 11240101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11240102, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 11240103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201902, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203131, "Rank": 5, "UpgradeSkills": {"112401211": [{"ConditionId": 11240104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11240105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11240106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "112701": [{"SkillId": 200742, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202772, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202482, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 200172, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 200741, "Rank": 3, "UpgradeSkills": {"112701111": [{"ConditionId": 11270101, "Type": 1, "Group": 1, "Requirement": 8, "AdditionalRequirement": 4}]}}, {"SkillId": 200012, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 202481, "Rank": 5, "UpgradeSkills": {"112701211": [{"ConditionId": 11270102, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 2}, {"ConditionId": 11270103, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 11270104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11270105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "113101": [{"SkillId": 203042, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201032, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203542, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202402, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 203041, "Rank": 3, "UpgradeSkills": {"113101111": [{"ConditionId": 11310101, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11310102, "Type": 1, "Group": 1, "Requirement": 6, "AdditionalRequirement": 4}]}}, {"SkillId": 203552, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203541, "Rank": 5, "UpgradeSkills": {"113101211": [{"ConditionId": 11310103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11310104, "Type": 4, "Group": 0, "Requirement": 2, "AdditionalRequirement": 0}, {"ConditionId": 11310105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11310106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "113201": [{"SkillId": 203622, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203332, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203632, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202602, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 203621, "Rank": 3, "UpgradeSkills": {"113201111": [{"ConditionId": 11320101, "Type": 1, "Group": 1, "Requirement": 3, "AdditionalRequirement": 3}, {"ConditionId": 11320102, "Type": 1, "Group": 1, "Requirement": 7, "AdditionalRequirement": 2}, {"ConditionId": 11320103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 201412, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203631, "Rank": 5, "UpgradeSkills": {"113201211": [{"ConditionId": 11320104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11320105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11320106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}], "113301": [{"SkillId": 202982, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 203442, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 201312, "Rank": 0, "UpgradeSkills": {}}, {"SkillId": 202472, "Rank": 2, "UpgradeSkills": {}}, {"SkillId": 202981, "Rank": 3, "UpgradeSkills": {"113301111": [{"ConditionId": 11330101, "Type": 1, "Group": 1, "Requirement": 2, "AdditionalRequirement": 3}, {"ConditionId": 11330102, "Type": 3, "Group": 0, "Requirement": 3, "AdditionalRequirement": 0}, {"ConditionId": 11330103, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11330104, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}, {"SkillId": 203642, "Rank": 4, "UpgradeSkills": {}}, {"SkillId": 203441, "Rank": 5, "UpgradeSkills": {"113301211": [{"ConditionId": 11330105, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11330106, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11330107, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}, {"ConditionId": 11330108, "Type": 0, "Group": 0, "Requirement": 0, "AdditionalRequirement": 0}]}}]}