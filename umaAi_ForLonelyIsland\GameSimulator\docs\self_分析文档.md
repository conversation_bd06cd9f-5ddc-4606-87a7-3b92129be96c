# 赛马娘训练数据库完整分析报告

## 📊 数据库结构总结

基于对`single_mode_training_effect`表的深入分析，本文档整理了赛马娘训练系统的完整数据结构。

**分析时间**: 2025年7月31日
**数据来源**: master.mdb数据库 - single_mode_training_effect表  
**总记录数**: 662条训练效果数据

## 🎯 核心发现

### 1. 剧本系统 (scenario_id)
- 数据库包含 **11个剧本** 的训练数据
- 每个剧本都有独立的训练效果配置
- URA剧本 (scenario_id=1) 和无人岛杯剧本 (scenario_id=11) 是主要分析对象

### 2. 基础训练系统 (command_id 101-106)

**正确的训练类型映射：**
- **101** = 速度训练
- **102** = 力量训练  
- **103** = 根性训练
- **105** = 耐力训练 (⚠️ 注意：跳过了104)
- **106** = 智力训练

**⚠️ 重要发现：编码不连续性**
- 缺失 command_id=104，这是设计特征，不是数据错误
- 编码顺序不按属性顺序排列

### 3. 属性映射系统 (target_type)
```
1  = 速度属性
2  = 耐力属性
3  = 力量属性
4  = 根性属性
5  = 智力属性
10 = 体力变化
20 = 干劲变化
30 = 技能点(pt)
```

### 4. 外出事件系统 (command_id 30*)
- **一般外出事件** - 影响干劲和体力恢复
- **304 合宿休息** - 特殊事件：+40体力 +1级干劲

### 5. 夏季合宿系统
- **时间**：第二年和第三年的7月上-8月下（4个回合）
- **特点**：所有训练都是满级(Lv5)

#### URA剧本夏季合宿 (command_id 601-605)
- **601** = 速度训练 Lv5
- **602** = 力量训练 Lv5 
- **603** = 根性训练 Lv5
- **604** = 耐力训练 Lv5
- **605** = 智力训练 Lv5

#### 无人岛杯剧本合宿 (command_id 3601-3606)
基于代码分析发现的映射关系：
- **3601** = 速度训练 Lv5 (对应101)
- **3602** = 耐力训练 Lv5 (对应105)
- **3603** = 力量训练 Lv5 (对应102)
- **3604** = 根性训练 Lv5 (对应103)
- **3605** = 智力训练 Lv5 (对应106)

### 6. 训练等级数据特点
- **数据库只记录Lv1基础数据**
- **Lv2-Lv5需要游戏内实测计算**
- **夏季合宿提供Lv5参考数据**

## 📈 关键数据示例 (URA剧本)

### 基础训练效果 (Lv1)
| 训练类型 | 主属性收益 | 副属性收益 | 体力消耗 | 技能点 |
|----------|------------|------------|----------|--------|
| 速度(101) | 速度+11 | 力量+6 | -21 | +4 |
| 力量(102) | 力量+9 | 耐力+6 | -20 | +4 |
| 根性(103) | 根性+8 | 力量+6 | -15 | +5 |
| 耐力(105) | 耐力+10 | 根性+4 | -20 | +5 |
| 智力(106) | 智力+9 | 速度+4 | -15 | +5 |

## 🔧 重要技术发现

### 1. 编码映射复杂性
不同剧本的合宿训练使用不同的command_id编码规则：
- URA: 601-605 (相对简单)
- 无人岛杯: 3601-3605 (完全不同的编码体系)

### 2. 数据完整性
- 基础训练数据完整 (所有剧本)
- 合宿训练数据完整 (URA和无人岛杯)
- 缺失中间等级数据 (Lv2-Lv4)

### 3. 系统设计特点
- 每个剧本独立配置训练效果
- 合宿系统提供固定的Lv5训练
- 外出事件独立于训练系统

## 🔍 无人岛杯合宿训练映射验证

基于代码分析 (`TurnInfoPioneer.cs`) 发现的映射关系：

```csharp
// 无人岛杯合宿训练映射
[3601] = 101,  // 速度训练
[3602] = 105,  // 耐力训练  
[3603] = 102,  // 力量训练
[3604] = 103,  // 根性训练
[3605] = 106   // 智力训练
```

这证实了无人岛杯的合宿训练与基础训练的对应关系。

## 📋 关键SQL查询

### 验证训练映射
```sql
-- 查看URA剧本基础训练数据
SELECT command_id, target_type, effect_value
FROM single_mode_training_effect 
WHERE scenario_id = 1 AND command_id IN (101,102,103,105,106) 
AND result_state = 2
ORDER BY command_id, target_type;

-- 查看无人岛杯合宿训练数据
SELECT command_id, target_type, effect_value
FROM single_mode_training_effect 
WHERE scenario_id = 11 AND command_id IN (3601,3602,3603,3604,3605)
AND result_state = 2
ORDER BY command_id, target_type;
```

### 验证编码完整性
```sql
-- 检查所有剧本的command_id分布
SELECT scenario_id, 
       GROUP_CONCAT(DISTINCT command_id ORDER BY command_id) as command_ids
FROM single_mode_training_effect 
WHERE command_id BETWEEN 101 AND 106
GROUP BY scenario_id 
ORDER BY scenario_id;
```

## ⚠️ 重要结论

### 1. 编码系统特点
- **基础训练**: 101,102,103,105,106 (跳过104)
- **URA合宿**: 601-605 (相对规律)
- **无人岛杯合宿**: 3601-3605 (独立编码体系)

### 2. 数据库设计原则
- 每个剧本独立存储训练效果
- 只存储关键等级数据 (Lv1和Lv5)
- 使用result_state区分不同状态

### 3. 开发建议
- **必须建立完整的command_id映射表**
- **不能依赖数学规律推导训练类型**
- **需要为每个剧本单独处理合宿训练映射**

## 📊 数据库记录统计

- **总记录数**: 662条
- **剧本数量**: 11个
- **基础训练类型**: 5种 (缺失104)
- **合宿训练系统**: 2套 (URA和无人岛杯)
- **属性类型**: 8种 (速耐力根智+体力干劲技能点)

---

*最后更新: 2025年7月31日*
*基于: single_mode_training_effect表完整分析*  
*状态: 数据结构分析完成，可用于开发参考*
