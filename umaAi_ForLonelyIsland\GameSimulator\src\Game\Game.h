#include<random>
#include<array>

struct URAGame {
    bool playerPrint;//是否是人在玩

    //int umaId;//马娘编号，见KnownUmas.cpp
    //bool isRacingTurn[TOTAL_TURN];//这回合是否比赛
    double iveStatusBonus[5];//马娘的五维属性的成长率 1.1 1.2

    int16_t turn;//回合数，从0开始，到77结束
    int16_t vital;//体力，叫做“vital”是因为游戏里就这样叫的
    int16_t maxVital;//体力上限
    int16_t motivation;//干劲，从1到5分别是绝不调到绝好调
    int16_t trainingPt;

    int16_t fiveStatus[5];//五维属性，1200以上不减半
    int16_t fiveStatusLimit[5];//五维属性上限，1200以上不减半
    int16_t skillPt;//技能点
    //int16_t skillScore;//已买技能的分数

    //float ptScoreRate;//每pt多少分
    //int16_t failureRateBias;//失败率改变量。练习上手=-2，练习下手=2

    //bool isAiJiao;//爱娇
    //bool isPositiveThinking;//ポジティブ思考，友人第三段出行选上的buff，可以防一次掉心情

    //int16_t zhongMaBlueCount[5];//种马的蓝因子个数，假设只有3星
    //int16_t zhongMaExtraBonus[6];//种马的剧本因子以及技能白因子（等效成pt），每次继承加多少。全大师杯因子典型值大约是30速30力200pt

    //int16_t saihou;//赛后加成
    bool isRacing;//这个回合是否在比赛

    //Person persons[MAX_HEAD_NUM];//最多9个头。依次是6张卡，理事长7，记8,别的友人卡9

    //int16_t personDistribution[5][5];//每个训练有哪些人头id，personDistribution[哪个训练][第几个人头]，空位置为-1
    //int16_t lockedTrainingId;//是否锁训练，以及锁在了哪个训练。可以先不加，等ai做完了有时间再加。
    
    //剧本相关
    //int16_t ura_trainingColor[5];//五种训练的等级

};

int RandomTraining();

void runURAGame();

//获取训练pt             是否友情训练        支援卡数量       pt加成 3% 5% 10% 15%
int getTrainingPt(bool isYouqingTraining,int cardsNumber,double ptUprate);