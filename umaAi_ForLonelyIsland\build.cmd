@echo off
echo ========================================
echo URA剧本模拟器 - 编译脚本
echo ========================================

REM 设置编译目录
if not exist "build" mkdir build
cd build

REM 清理旧文件
if exist "*.obj" del *.obj
if exist "*.exe" del *.exe

echo 正在编译...

REM 编译源文件
cl /EHsc /std:c++17 /I"..\src" ^
   ..\src\GameDatabase\GameConstants.cpp ^
   ..\src\Game\Game.cpp ^
   ..\Tools\test_game.cpp ^
   /Fe:ura_simulator.exe

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo 编译成功！
    echo 可执行文件: build\ura_simulator.exe
    echo ========================================
    echo.
    echo 运行测试:
    echo cd build
    echo ura_simulator.exe
) else (
    echo.
    echo ========================================
    echo 编译失败！请检查错误信息。
    echo ========================================
)

cd ..
pause
