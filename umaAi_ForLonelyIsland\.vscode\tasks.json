{"version": "2.0.0", "tasks": [{"type": "shell", "label": "C/C++: g++.exe build active file", "command": "g++", "args": ["-std=c++17", "-Wall", "-Wextra", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "使用MinGW-w64 GCC编译当前活动文件"}, {"type": "cppbuild", "label": "C/C++: gcc.exe 生成活动文件", "command": "C:/msys64/ucrt64/bin/g++.exe", "args": ["-fdiagnostics-color=always", "-g", "-std=c++17", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "Task generated by <PERSON>bugger."}, {"label": "Build URA Project", "type": "shell", "command": "${workspaceFolder}/build.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "构建整个URA项目"}, {"label": "Build and Run Current File", "type": "shell", "command": "C:/msys64/ucrt64/bin/g++.exe", "args": ["-std=c++17", "-Wall", "-Wextra", "-I${workspaceFolder}", "-I${workspaceFolder}/GameSimulator", "-I${workspaceFolder}/GameSimulator/URA1.0,0", "-I${workspaceFolder}/GameSimulator/URA1.0.1", "-I${workspaceFolder}/GameSimulator/URA1.0.1/Game", "-I${workspaceFolder}/GameSimulator/URA1.0.1/GameDatabase", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe", "&&", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "使用GCC编译并运行当前文件"}, {"label": "Clean Build", "type": "shell", "command": "${workspaceFolder}/cleanup_project.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "清理构建文件"}]}