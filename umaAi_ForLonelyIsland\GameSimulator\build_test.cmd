@echo off
echo 编译URA测试程序...

REM 设置Visual Studio环境（如果需要）
where cl >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在设置Visual Studio环境...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo 错误：找不到Visual Studio 2022，请确保已安装或使用Developer PowerShell运行
        pause
        exit /b 1
    )
)

REM 确保build目录存在
if not exist "build" mkdir build
cd build

REM 清理旧文件
if exist "test_runner.exe" del test_runner.exe

echo 正在编译...
cl /EHsc /std:c++17 /I"..\src" ..\src\Game\Game.cpp ..\tools\test_runner.cpp /Fe:test_runner.exe

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo 编译成功！运行测试...
    echo ========================================
    echo.
    test_runner.exe
) else (
    echo.
    echo ========================================
    echo 编译失败！请检查错误信息。
    echo ========================================
)

cd ..
pause