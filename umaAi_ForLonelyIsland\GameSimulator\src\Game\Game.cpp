#include <iostream>
#include <cassert>
#include <random>
#include "Game.h"
using namespace std;

static bool randBool(mt19937_64& rand, double p)
{
  return rand() % 65536 < p * 65536;
}



void runURAGame(){
  URAGame game;

  //初始化游戏状态
  game.turn = 0;
  game.vital = 100;
  game.motivation = 3;//普通干劲0加成

  for(int i = 0;i < 5;i++){
    game.fiveStatus[i] = 100;//给5维度赋值为100
  }

  game.skillPt = 150;//初始pt应该都是150

  for(int turn = 0; turn < 78; turn++){
    game.turn = turn;

    cout << "=== 第" << turn + 1 << "回合 ==="
    <<endl;

    cout << "体力:" << game.vital << endl;
    cout << "速度:" << game.fiveStatus[0] << endl;
    cout << "耐力:" << game.fiveStatus[1] << endl;
    cout << "力量:" << game.fiveStatus[2] << endl;
    cout << "根性:" << game.fiveStatus[3] << endl;
    cout << "智力:" << game.fiveStatus[4] << endl;

    if (game.vital < 30)
    {
      game.vital += 50;
      cout << "休息,体力恢复到:" << game.vital << endl;
    }
    else
    {
      // 简单的训练逻辑
      switch (RandomTraining())
      {
      case 1:
        game.fiveStatus[0] += 100;
        cout << "进行速度训练速度增加100" << endl;
        break;
      case 2:
        game.fiveStatus[1] += 100;
        cout << "进行耐力训练耐力增加100" << endl;
        break;
      case 3:
        game.fiveStatus[2] += 100;
        cout << "进行力量训练力量增加100" << endl;
        break;
      case 4:
        game.fiveStatus[3] += 100;
        cout << "进行根性训练根性增加100" << endl;
        break;
      case 5:
        game.fiveStatus[4] += 100;
        cout << "进行智力训练智力增加100" << endl;
        break;
      }
      //体力减少
      game.vital -= 20;
    }
  }

  cout << "游戏结束!" << endl;

  for (int i = 0; i < 5; i++)
  {
    cout << game.fiveStatus[i] << endl;
  }
  
}


//简单的随机数生成器
int RandomTraining(){
  random_device rd;

  mt19937 rng(rd());

  uniform_int_distribution<int>dist(1,5);

  int randomNumber = dist(rng);

  return randomNumber;
}

//训练pt = 73 + 40 × 加成率 + 总卡数 × (7 + 10 × 加成率)

//获取训练pt             是否友情训练        支援卡数量       pt加成率 3% 5% 10% 15%
int getTrainingPt(bool isYouqingTraining,int cardsNumber,double ptUprate){
  double trainingPt; 
  if (isYouqingTraining)
  {
    trainingPt = 73 + 40 * ptUprate + cardsNumber * ( 7 + 10 * ptUprate);
  }else{
    trainingPt =(60 + cardsNumber * 6) * (1 + ptUprate);
  }
  
  int16_t pt = (int16_t)trainingPt;

  return pt;
}
//tips 此公式为我个人推导，经验证，友情训练时可能存在1点pt的误差，不过概率很低 






