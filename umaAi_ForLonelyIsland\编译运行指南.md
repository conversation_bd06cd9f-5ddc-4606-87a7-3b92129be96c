# 🚀 URA剧本AI - 编译运行指南

## 📋 编译步骤

### 方法1：使用Developer Command Prompt（推荐）

1. **打开Developer Command Prompt**
   - 按Windows键，搜索"Developer Command Prompt"
   - 选择"Developer Command Prompt for VS 2022"

2. **导航到项目目录**
   ```cmd
   C:
   cd "C:\Users\<USER>\Desktop\URA源代码\umaAi_ForURA"
   ```

3. **运行编译脚本**
   ```cmd
   compile.cmd
   ```

### 方法2：手动编译命令

如果脚本不工作，可以手动输入编译命令：

```cmd
# 创建输出目录
mkdir bin

# 编译项目
cl /EHsc /std:c++17 GameSimulator\Game.cpp Tools\test_game.cpp /Fe:bin\test_game.exe /Fo:bin\ /nologo
```

### 方法3：使用Visual Studio

1. 打开Visual Studio 2022
2. 创建新项目 → 控制台应用 (C++)
3. 将以下文件添加到项目：
   - `GameSimulator/Game.h`
   - `GameSimulator/Game.cpp`  
   - `Tools/test_game.cpp`
4. 按Ctrl+F5运行

## 🎯 运行测试

编译成功后：

```cmd
cd bin
test_game.exe
```

## 📊 预期输出

运行成功后会看到：

```
URA Script AI Test Program
1. Run single game (detailed mode)
2. Run single game (fast mode)  
3. Run benchmark (100 games)
Please select (1-3):
```

选择选项后会看到游戏运行过程：

```
Starting new URA script game!
Character ID: 1
Support cards count: 6

=== Game Status ===
Turn: 1/78
Vital: 100/100
Motivation: 3/5
Attributes: Speed100 Stamina100 Power100 Guts100 Wisdom100
Skill Points: 0
Fan Count: 0
Current Score: 500

Support Card Status:
  Card1: Friendship0 [Speed]
  Card2: Friendship0 [Stamina]
  ...

>>> AI Choice: Speed Training
Training successful! Speed Training +15
```

## 🐛 常见问题解决

### 问题1：找不到cl编译器

**错误信息**：
```
'cl' is not recognized as an internal or external command
```

**解决方案**：
- 确保在"Developer Command Prompt for VS 2022"中运行
- 或者先运行：`"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"`

### 问题2：编译错误

**可能原因**：
- 文件路径问题
- C++标准版本问题
- 缺少头文件

**解决方案**：
1. 检查所有文件是否存在
2. 确保使用C++17标准
3. 检查include路径

### 问题3：运行时崩溃

**可能原因**：
- 数组越界
- 空指针访问
- 随机数种子问题

**调试方法**：
1. 在Visual Studio中运行调试版本
2. 添加更多cout输出
3. 检查数组访问边界

## 🔧 自定义编译选项

### Debug版本（用于调试）
```cmd
cl /EHsc /std:c++17 /Zi /Od GameSimulator\Game.cpp Tools\test_game.cpp /Fe:bin\test_game_debug.exe /Fo:bin\
```

### Release版本（优化性能）
```cmd
cl /EHsc /std:c++17 /O2 /DNDEBUG GameSimulator\Game.cpp Tools\test_game.cpp /Fe:bin\test_game_release.exe /Fo:bin\
```

### 添加更多警告
```cmd
cl /EHsc /std:c++17 /W4 GameSimulator\Game.cpp Tools\test_game.cpp /Fe:bin\test_game.exe /Fo:bin\
```

## 📁 文件结构检查

确保您的项目目录结构如下：

```
umaAi_ForURA/
├── GameSimulator/
│   ├── Game.h          ✓ 必需
│   └── Game.cpp        ✓ 必需
├── Tools/
│   └── test_game.cpp   ✓ 必需
├── Config/
│   └── game_config.json
├── build.bat
├── compile.cmd         ✓ 新的编译脚本
└── README.md
```

## 🎮 测试选项说明

### 选项1：详细模式
- 逐回合显示游戏状态
- 显示AI决策过程
- 可以观察每个细节
- 适合学习和调试

### 选项2：快速模式  
- 快速完成一局游戏
- 只显示关键信息
- 适合快速测试

### 选项3：性能测试
- 运行100局游戏
- 统计平均表现
- 测试AI稳定性
- 适合评估AI水平

## 🎯 成功标志

如果看到以下输出，说明项目运行成功：

```
Game ended!
Final Score: 1250
Actions executed: 65
Race wins: 6/9
```

## 📈 下一步

项目运行成功后，您可以：

1. **观察AI行为** - 理解AI如何做决策
2. **修改参数** - 调整训练收益、体力消耗等
3. **改进AI逻辑** - 让AI更聪明
4. **添加新功能** - 实现更多游戏机制

## 💡 开发提示

1. **每次修改后重新编译** - 使用`compile.cmd`
2. **保存重要版本** - 复制bin目录作为备份
3. **记录修改** - 记下每次改动的效果
4. **小步测试** - 每次只改一点点

---

**准备好开始您的AI开发之旅了吗？** 🚀

现在就打开Developer Command Prompt，运行`compile.cmd`吧！
