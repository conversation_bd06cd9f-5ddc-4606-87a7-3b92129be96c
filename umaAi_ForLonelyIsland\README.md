# 赛马娘URA剧本AI项目

## 项目简介

基于UmaAI的设计思路，专门为赛马娘URA剧本开发的AI系统。URA剧本是最基础的剧本，适合作为学习游戏AI的入门项目。

## 项目特点

- 🎯 **专注URA剧本** - 简化复杂机制，专注基础训练
- 🚀 **快速上手** - 提供完整的开发指导
- 📚 **学习友好** - 详细的代码注释和文档
- 🛠️ **渐进开发** - 从简单到复杂，逐步完善

## 项目结构

```
umaAi_ForURA/
├── README.md                    # 项目说明
├── GameSimulator/              # 游戏模拟器（C++）
│   ├── Game.h/cpp              # 核心游戏逻辑
│   ├── UmaData.h/cpp           # 角色数据
│   ├── SupportCard.h/cpp       # 支援卡系统
│   └── Action.h/cpp            # 动作系统
├── AI/                         # AI模块
│   ├── SimpleAI.cpp            # 简单AI（规则基础）
│   ├── NeuralNetwork/          # 神经网络AI
│   │   ├── model.py            # PyTorch模型
│   │   ├── train.py            # 训练脚本
│   │   └── inference.cpp       # C++推理
│   └── MCTS/                   # 蒙特卡洛树搜索
│       └── mcts.cpp            # MCTS实现
├── Data/                       # 数据文件
│   ├── characters.json         # 角色数据
│   ├── support_cards.json      # 支援卡数据
│   ├── races.json              # 比赛数据
│   └── events.json             # 事件数据
├── Tools/                      # 开发工具
│   ├── data_crawler.py         # 数据爬取
│   ├── test_game.cpp           # 游戏测试
│   └── benchmark.py            # 性能测试
├── UI/                         # 用户界面
│   ├── console_ui.cpp          # 控制台界面
│   └── simple_gui.py           # 简单图形界面
└── Config/                     # 配置文件
    ├── game_config.json        # 游戏配置
    └── ai_config.json          # AI配置
```

## 开发阶段

### 阶段1：基础框架（当前）
- [x] 项目结构创建
- [ ] 基础游戏状态定义
- [ ] 简单的训练逻辑
- [ ] 控制台测试界面

### 阶段2：核心功能
- [ ] 完整的游戏模拟器
- [ ] 支援卡系统
- [ ] 事件处理
- [ ] 简单AI实现

### 阶段3：AI开发
- [ ] 规则基础AI
- [ ] 神经网络模型
- [ ] 自对弈训练
- [ ] 性能优化

### 阶段4：完善项目
- [ ] 用户界面
- [ ] 数据可视化
- [ ] 性能测试
- [ ] 文档完善

## 快速开始

### 环境要求
- Visual Studio 2022 (C++)
- Python 3.8+ (AI训练)
- Git (版本控制)

### 编译运行
```bash
# 编译C++代码
cd GameSimulator
cl /EHsc /std:c++17 test_game.cpp Game.cpp -o test_game.exe

# 运行测试
test_game.exe

# Python AI训练
cd AI/NeuralNetwork
python train.py
```

## 学习路径

1. **理解游戏机制** - 从简单的URA剧本开始
2. **实现游戏模拟器** - 用C++模拟游戏逻辑
3. **开发简单AI** - 基于规则的决策系统
4. **学习神经网络** - 逐步理解深度学习
5. **训练AI模型** - 通过自对弈提升AI水平

## 贡献指南

这是一个学习项目，欢迎：
- 提出问题和建议
- 分享学习心得
- 改进代码实现
- 完善文档说明

## 许可证

本项目仅用于学习和研究目的，请勿用于商业用途。

---

**开始您的游戏AI开发之旅！** 🚀
