# 🏇 URA剧本AI - 快速开始指南

## 🎯 项目现状

恭喜！您已经有了一个可以运行的URA剧本游戏模拟器！

### ✅ 已完成的功能
- **基础游戏状态** - 完整的URA剧本游戏状态定义
- **训练系统** - 5种基础训练（速度、耐力、力量、根性、智慧）
- **支援卡系统** - 支援卡分配、友情度、闪光机制
- **比赛系统** - 固定回合的比赛安排
- **事件系统** - 基础的随机事件处理
- **简单AI** - 两种测试AI（随机AI和智能AI）
- **测试程序** - 完整的游戏测试和性能评估

### 🔄 当前可以做什么
- 运行完整的78回合URA剧本游戏
- 测试不同的AI策略
- 观察游戏状态变化
- 评估AI性能

## 🚀 立即运行

### 第一步：编译项目
```cmd
# 在项目根目录下运行
build.bat
```

### 第二步：运行测试
```cmd
cd bin
test_game.exe
```

### 第三步：选择测试模式
- **选项1**: 详细模式 - 逐回合显示游戏状态
- **选项2**: 快速模式 - 快速完成一局游戏
- **选项3**: 性能测试 - 运行100局游戏统计

## 📊 预期结果

运行成功后，您会看到：

```
🏇 开始URA剧本测试游戏 🏇
开始新的URA剧本游戏！
角色ID: 1
支援卡数量: 6

=== 游戏状态 ===
回合: 1/78
体力: 100/100
干劲: 3/5
属性: 速100 耐100 力100 根100 智100
技能点: 0
粉丝数: 0
当前评分: 500

支援卡状态:
  卡1: 友情度0 [速度]
  卡2: 友情度0 [耐力]
  ...

>>> AI选择: 速度训练
训练成功！速度训练 +15
```

## 🎓 学习路径

### 阶段1：理解现有代码（当前）
1. **运行测试程序** - 看看AI是如何工作的
2. **阅读Game.h** - 理解游戏状态结构
3. **阅读Game.cpp** - 理解游戏逻辑实现
4. **观察AI决策** - 看SimpleSmartAI如何选择动作

### 阶段2：改进游戏逻辑（1-2周）
1. **完善训练公式** - 让训练收益更真实
2. **添加更多事件** - 丰富游戏体验
3. **改进比赛系统** - 更复杂的胜负判定
4. **平衡性调整** - 让游戏更有挑战性

### 阶段3：开发更好的AI（2-3周）
1. **改进规则AI** - 更智能的决策逻辑
2. **学习神经网络基础** - 理解深度学习原理
3. **实现简单神经网络** - 用Python训练模型
4. **集成到C++** - 在游戏中使用训练好的模型

### 阶段4：高级功能（3-4周）
1. **自对弈训练** - 让AI自己学习
2. **蒙特卡洛树搜索** - 更强的搜索算法
3. **用户界面** - 图形化界面
4. **性能优化** - 让AI运行更快

## 🛠️ 代码结构说明

### 核心文件
- `GameSimulator/Game.h` - 游戏状态和接口定义
- `GameSimulator/Game.cpp` - 游戏逻辑实现
- `Tools/test_game.cpp` - 测试程序和简单AI

### 关键类和结构
```cpp
struct URAGame {
    // 游戏状态：回合、体力、属性等
};

class URAGameSimulator {
    // 游戏模拟器：执行动作、推进回合
};

class SimpleSmartAI {
    // 简单AI：基于规则的决策
};
```

## 🔧 自定义和扩展

### 修改AI策略
在`test_game.cpp`中找到`SimpleSmartAI::selectAction`方法：

```cpp
Action selectAction(const URAGameSimulator& simulator) {
    // 在这里修改AI的决策逻辑
    // 例如：优先训练某个属性
    // 例如：更智能的体力管理
    // 例如：考虑支援卡闪光状态
}
```

### 调整游戏参数
在`Game.cpp`中修改各种数值：

```cpp
// 修改训练体力消耗
int stamina_cost = 20;  // 改为其他值

// 修改训练基础收益
int base_gain = game_state.train_base_value[training_type][head_count];

// 修改失败率
int fail_rate = game_state.train_fail_rate[training_type];
```

### 添加新功能
1. **新的训练类型** - 在ActionType枚举中添加
2. **新的事件** - 在processEvent方法中添加
3. **新的AI策略** - 创建新的AI类

## 🐛 常见问题

### Q: 编译失败怎么办？
A: 确保在Developer Command Prompt中运行，或者先运行vcvars64.bat

### Q: 程序运行异常怎么办？
A: 检查是否有数组越界、空指针等问题，可以添加更多调试输出

### Q: AI表现不好怎么办？
A: 这是正常的！当前的AI很简单，后续我们会逐步改进

### Q: 想添加新功能怎么办？
A: 先理解现有代码，然后小步骤地添加新功能，每次修改后都要测试

## 🎯 下一步计划

### 立即可做的改进
1. **改进AI决策** - 让AI考虑更多因素
2. **添加调试信息** - 显示AI的决策理由
3. **调整数值平衡** - 让游戏更有趣

### 中期目标
1. **数据驱动** - 从JSON文件读取角色和支援卡数据
2. **更真实的公式** - 参考真实游戏的计算公式
3. **图形界面** - 用Python创建简单的GUI

### 长期目标
1. **神经网络AI** - 用深度学习训练AI
2. **自对弈系统** - 让AI自己学习和进化
3. **完整的URA模拟** - 接近真实游戏的体验

## 💡 学习建议

### 对于编程新手
1. **先运行起来** - 不要急于理解所有代码
2. **小步修改** - 每次只改一点点，立即测试
3. **多做实验** - 尝试不同的参数和策略
4. **记录问题** - 遇到问题时记录下来，方便后续解决

### 对于想学AI的同学
1. **理解问题** - 先理解游戏AI要解决什么问题
2. **从简单开始** - 先做好规则AI，再学神经网络
3. **实践为主** - 边做边学，不要只看理论
4. **循序渐进** - 不要急于实现复杂功能

## 🎉 恭喜！

您已经有了一个可以工作的游戏AI项目！这是一个很好的开始。

现在就去运行`build.bat`，然后享受您的第一个URA剧本AI吧！

有任何问题都可以随时询问，我会帮助您一步步完善这个项目。 🚀
