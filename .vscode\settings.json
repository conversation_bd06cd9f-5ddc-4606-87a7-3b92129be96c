{"dotnet.preferCSharpExtension": true, "files.associations": {"ostream": "cpp", "iostream": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "compare": "cpp", "concepts": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "forward_list": "cpp", "map": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "format": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "numbers": "cpp", "ranges": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "text_encoding": "cpp", "cinttypes": "cpp", "typeinfo": "cpp", "valarray": "cpp", "variant": "cpp", "cassert": "cpp"}}