{"files.associations": {"*.cpp": "cpp", "*.h": "c", "*.hpp": "cpp", "*.c": "c", "GameConstants.cpp": "cpp"}, "files.encoding": "utf8", "files.autoGuessEncoding": true, "files.eol": "\r\n", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.cStandard": "c17", "C_Cpp.default.intelliSenseMode": "gcc-x64", "C_Cpp.default.compilerPath": "C:/msys64/ucrt64/bin/gcc.exe", "C_Cpp.errorSquiggles": "enabled", "C_Cpp.intelliSenseEngine": "default", "C_Cpp.autocomplete": "default", "C_Cpp.intelliSenseUpdateDelay": 2000, "C_Cpp.workspaceParsingPriority": "highest", "C_Cpp.enhancedColorization": "enabled", "code-runner.executorMap": {"cpp": "cd $dir && g++ -std=c++17 -Wall -Wextra -I\"${workspaceFolder}\" -I\"${workspaceFolder}/GameSimulator\" -I\"${workspaceFolder}/GameSimulator/URA1.0,0\" -I\"${workspaceFolder}/GameSimulator/URA1.0.1\" -I\"${workspaceFolder}/GameSimulator/URA1.0.1/Game\" -I\"${workspaceFolder}/GameSimulator/URA1.0.1/GameDatabase\" $fileName -o $fileNameWithoutExt.exe && $fileNameWithoutExt.exe"}, "code-runner.runInTerminal": true, "code-runner.saveFileBeforeRun": true, "code-runner.clearPreviousOutput": true, "code-runner.preserveFocus": false, "terminal.integrated.defaultProfile.windows": "Developer PowerShell for VS 2022", "terminal.integrated.profiles.windows": {"Developer PowerShell for VS 2022": {"path": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\VsDevCmd.bat", "args": ["&", "powershell"], "icon": "terminal-powershell"}}, "cmake.configureOnOpen": false, "cmake.configureOnEdit": false, "cmake.automaticReconfigure": false, "cmake.showOptionsMovedNotification": false, "extensions.ignoreRecommendations": false, "editor.formatOnSave": true, "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": true}