#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
休息机制分析工具
分析master.mdb中与休息相关的数据表和字段
"""

import sqlite3
import os

def analyze_training_system():
    """重新分析训练系统的完整实现"""

    # 连接数据库
    mdb_path = r"c:\Users\<USER>\Desktop\URA源代码\umaAi_ForURA\GameSimulator\data\Database\mdb\master.mdb"
    if not os.path.exists(mdb_path):
        print(f"❌ 数据库文件不存在: {mdb_path}")
        print(f"当前工作目录: {os.getcwd()}")
        return

    conn = sqlite3.connect(mdb_path)
    cursor = conn.cursor()

    print("🔍 重新分析训练系统完整数据...")
    print("=" * 60)
    
    # 1. 首先验证command_id=106的真实含义
    print("\n=== 1. 验证command_id=106的真实含义 ===")

    # 查看所有command_id的分布
    cursor.execute("""
        SELECT DISTINCT command_id, COUNT(*) as count
        FROM single_mode_training_effect
        GROUP BY command_id
        ORDER BY command_id
    """)
    all_commands = cursor.fetchall()
    print("所有command_id分布:")
    for cmd_id, count in all_commands:
        print(f"  command_id {cmd_id}: {count} 条记录")

    # 分析command_id的模式
    print("\n--- 分析command_id模式 ---")
    basic_commands = [cmd for cmd, _ in all_commands if 100 <= cmd <= 110]
    advanced_commands = [cmd for cmd, _ in all_commands if 600 <= cmd <= 610]
    special_commands = [cmd for cmd, _ in all_commands if 3600 <= cmd <= 3610]

    print(f"基础训练 (100-110): {basic_commands}")
    print(f"高级训练 (600-610): {advanced_commands}")
    print(f"特殊训练 (3600-3610): {special_commands}")
    
    # 2. 详细分析command_id=106的数据特征
    print("\n=== 2. 详细分析command_id=106的数据特征 ===")
    try:
        cursor.execute("""
            SELECT scenario_id, sub_id, result_state, target_type, effect_value
            FROM single_mode_training_effect
            WHERE command_id = 106
            ORDER BY scenario_id, sub_id, result_state, target_type
        """)
        cmd106_records = cursor.fetchall()

        print(f"📊 command_id=106 共有 {len(cmd106_records)} 条记录:")
        print("scenario_id | sub_id | result_state | target_type | effect_value | 含义")
        print("-" * 80)

        for record in cmd106_records:
            scenario_id, sub_id, result_state, target_type, effect_value = record

            # 解释target_type
            target_meaning = {
                1: "速度", 2: "耐力", 3: "力量", 4: "根性", 5: "智慧",
                10: "体力", 30: "技能点"
            }.get(target_type, f"未知({target_type})")

            print(f"{scenario_id:11} | {sub_id:6} | {result_state:12} | {target_type:11} | {effect_value:12} | {target_meaning}")

        # 分析数据模式
        print("\n--- 数据模式分析 ---")
        cursor.execute("""
            SELECT scenario_id,
                   GROUP_CONCAT(target_type || ':' || effect_value) as effects
            FROM single_mode_training_effect
            WHERE command_id = 106 AND result_state = 2
            GROUP BY scenario_id
            ORDER BY scenario_id
        """)
        pattern_records = cursor.fetchall()

        for scenario_id, effects in pattern_records:
            print(f"剧本{scenario_id}: {effects}")

    except Exception as e:
        print(f"❌ 查询command_id=106数据失败: {e}")

    # 3. 对比其他训练类型的数据模式
    print("\n=== 3. 对比其他训练类型的数据模式 ===")
    try:
        for cmd_id in [101, 102, 103, 105]:  # 速度、耐力、力量、智慧
            cursor.execute("""
                SELECT scenario_id,
                       GROUP_CONCAT(target_type || ':' || effect_value) as effects
                FROM single_mode_training_effect
                WHERE command_id = ? AND result_state = 2 AND scenario_id IN (1, 11)
                GROUP BY scenario_id
                ORDER BY scenario_id
            """, (cmd_id,))

            cmd_records = cursor.fetchall()
            cmd_name = {101: "速度", 102: "耐力", 103: "力量", 105: "智慧"}.get(cmd_id)

            print(f"\n{cmd_name}训练 (command_id={cmd_id}):")
            for scenario_id, effects in cmd_records:
                print(f"  剧本{scenario_id}: {effects}")

    except Exception as e:
        print(f"❌ 查询其他训练类型失败: {e}")
    
    # 4. 调查完整的训练等级系统
    print("\n=== 4. 调查完整的训练等级系统 ===")
    try:
        # 检查是否存在所有等级的训练数据
        print("检查URA剧本(scenario_id=1)的完整训练数据:")

        # 查看所有可能的训练相关command_id
        cursor.execute("""
            SELECT DISTINCT command_id, COUNT(*) as count
            FROM single_mode_training_effect
            WHERE scenario_id = 1 AND command_id BETWEEN 100 AND 700
            GROUP BY command_id
            ORDER BY command_id
        """)
        ura_commands = cursor.fetchall()

        print("URA剧本的所有command_id:")
        for cmd_id, count in ura_commands:
            print(f"  command_id {cmd_id}: {count} 条记录")

        # 检查sub_id字段是否用于区分等级
        print("\n检查sub_id字段的使用模式:")
        cursor.execute("""
            SELECT command_id, sub_id, COUNT(*) as count
            FROM single_mode_training_effect
            WHERE scenario_id = 1 AND command_id IN (101, 102, 103, 105, 106)
            GROUP BY command_id, sub_id
            ORDER BY command_id, sub_id
        """)
        sub_id_patterns = cursor.fetchall()

        for cmd_id, sub_id, count in sub_id_patterns:
            cmd_name = {101: "速度", 102: "耐力", 103: "力量", 105: "智慧", 106: "疑似休息"}.get(cmd_id, f"未知{cmd_id}")
            print(f"  {cmd_name} (command_id={cmd_id}), sub_id={sub_id}: {count} 条记录")

    except Exception as e:
        print(f"❌ 调查训练等级系统失败: {e}")

    # 5. 检查无人岛杯的训练数据
    print("\n=== 5. 检查无人岛杯的训练数据 ===")
    try:
        print("检查无人岛杯(scenario_id=11)的完整训练数据:")

        cursor.execute("""
            SELECT DISTINCT command_id, COUNT(*) as count
            FROM single_mode_training_effect
            WHERE scenario_id = 11 AND command_id BETWEEN 3600 AND 3700
            GROUP BY command_id
            ORDER BY command_id
        """)
        island_commands = cursor.fetchall()

        print("无人岛杯的特殊command_id:")
        for cmd_id, count in island_commands:
            print(f"  command_id {cmd_id}: {count} 条记录")

        # 检查是否有对应的等级数据
        if island_commands:
            print("\n检查无人岛杯的sub_id模式:")
            cursor.execute("""
                SELECT command_id, sub_id, COUNT(*) as count
                FROM single_mode_training_effect
                WHERE scenario_id = 11 AND command_id BETWEEN 3600 AND 3700
                GROUP BY command_id, sub_id
                ORDER BY command_id, sub_id
            """)
            island_sub_patterns = cursor.fetchall()

            for cmd_id, sub_id, count in island_sub_patterns:
                print(f"  command_id={cmd_id}, sub_id={sub_id}: {count} 条记录")

    except Exception as e:
        print(f"❌ 检查无人岛杯数据失败: {e}")
    
    # 6. 验证训练等级数据的完整性
    print("\n=== 6. 验证训练等级数据的完整性 ===")
    try:
        # 检查是否真的存在Lv1-Lv5的完整数据
        print("验证URA剧本是否有完整的Lv1-Lv5数据:")

        # 假设sub_id代表等级
        for cmd_id in [101, 102, 103, 105]:  # 速度、耐力、力量、智慧
            cmd_name = {101: "速度", 102: "耐力", 103: "力量", 105: "智慧"}.get(cmd_id)

            cursor.execute("""
                SELECT sub_id, COUNT(*) as count
                FROM single_mode_training_effect
                WHERE scenario_id = 1 AND command_id = ?
                GROUP BY sub_id
                ORDER BY sub_id
            """, (cmd_id,))

            level_data = cursor.fetchall()
            print(f"\n{cmd_name}训练的等级数据:")
            for sub_id, count in level_data:
                print(f"  等级{sub_id}: {count} 条记录")

        # 检查高级训练数据 (600系列)
        print("\n检查高级训练数据 (600系列):")
        cursor.execute("""
            SELECT command_id, sub_id, COUNT(*) as count
            FROM single_mode_training_effect
            WHERE scenario_id = 1 AND command_id BETWEEN 600 AND 610
            GROUP BY command_id, sub_id
            ORDER BY command_id, sub_id
        """)

        advanced_data = cursor.fetchall()
        for cmd_id, sub_id, count in advanced_data:
            print(f"  command_id={cmd_id}, sub_id={sub_id}: {count} 条记录")

    except Exception as e:
        print(f"❌ 验证训练等级数据失败: {e}")

    # 7. 寻找真正的休息机制
    print("\n=== 7. 寻找真正的休息机制 ===")
    try:
        # 搜索可能包含休息相关的表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        all_tables = [table[0] for table in cursor.fetchall()]

        rest_keywords = ['rest', 'sleep', 'recovery', 'heal', 'break']
        potential_rest_tables = []

        for table_name in all_tables:
            for keyword in rest_keywords:
                if keyword in table_name.lower():
                    potential_rest_tables.append(table_name)
                    break

        if potential_rest_tables:
            print("可能包含休息机制的表:")
            for table_name in potential_rest_tables:
                print(f"  📋 {table_name}")
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"    记录数: {count}")
                except:
                    print(f"    ❌ 无法访问")
        else:
            print("❌ 没有找到明显的休息相关表")

        # 检查是否有其他command_id可能代表休息
        print("\n检查所有剧本中缺失的command_id (可能是休息):")
        cursor.execute("""
            SELECT DISTINCT scenario_id
            FROM single_mode_training_effect
            ORDER BY scenario_id
        """)
        scenarios = [row[0] for row in cursor.fetchall()]

        for scenario_id in scenarios[:3]:  # 只检查前3个剧本
            cursor.execute("""
                SELECT DISTINCT command_id
                FROM single_mode_training_effect
                WHERE scenario_id = ?
                ORDER BY command_id
            """, (scenario_id,))

            scenario_commands = [row[0] for row in cursor.fetchall()]
            basic_training = [101, 102, 103, 104, 105]  # 包括根性104
            missing_commands = [cmd for cmd in basic_training if cmd not in scenario_commands]

            print(f"  剧本{scenario_id}: 缺失的基础训练command_id: {missing_commands}")

    except Exception as e:
        print(f"❌ 寻找休息机制失败: {e}")

    conn.close()
    print("\n✅ 重新分析完成")

if __name__ == "__main__":
    analyze_training_system()
